package com.hvisions.bpm.module.bpm.rawmaterial.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 原辅料仓库可视化数据传输对象
 * 
 * <AUTHOR>
 * @date 2025-09-15
 */
@Data
@ApiModel(description = "原辅料仓库可视化数据")
public class RawMaterialWarehouseVisualizationDTO {

    @ApiModelProperty(value = "原辅料仓库信息")
    private RawMaterialWarehouseDTO rawMaterialWarehouse;

    @ApiModelProperty(value = "前处理储存仓信息")
    private PreProcessingStorageDTO preProcessingStorage;

    @ApiModelProperty(value = "后处理暂存仓信息")
    private PostProcessingStorageDTO postProcessingStorage;

    @ApiModelProperty(value = "车间信息列表")
    private List<WorkshopDTO> workshops;

    /**
     * 原辅料仓库信息
     */
    @Data
    @ApiModel(description = "原辅料仓库信息")
    public static class RawMaterialWarehouseDTO {
        @ApiModelProperty(value = "总库存(KG)")
        private BigDecimal totalStock;

        @ApiModelProperty(value = "物料等级列表")
        private List<MaterialGradeDTO> materialGrades;
    }

    /**
     * 物料等级信息
     */
    @Data
    @ApiModel(description = "物料等级信息")
    public static class MaterialGradeDTO {
        @ApiModelProperty(value = "物料代码")
        private String materialCode;

        @ApiModelProperty(value = "物料名称")
        private String materialName;

        @ApiModelProperty(value = "当前库存(KG)")
        private BigDecimal currentStock;

        @ApiModelProperty(value = "安全库存(KG)")
        private BigDecimal safetyStock;

        @ApiModelProperty(value = "最大容量(KG)")
        private BigDecimal maxCapacity;
    }

    /**
     * 前处理储存仓信息
     */
    @Data
    @ApiModel(description = "前处理储存仓信息")
    public static class PreProcessingStorageDTO {
        @ApiModelProperty(value = "总存存(KG)")
        private BigDecimal totalStorage;

        @ApiModelProperty(value = "最大容量(KG)")
        private BigDecimal maxCapacity;

        @ApiModelProperty(value = "物料等级列表")
        private List<MaterialGradeDTO> materialGrades;
    }

    /**
     * 后处理暂存仓信息
     */
    @Data
    @ApiModel(description = "后处理暂存仓信息")
    public static class PostProcessingStorageDTO {
        @ApiModelProperty(value = "总存存(KG)")
        private BigDecimal totalStorage;

        @ApiModelProperty(value = "筒仓列表")
        private List<SiloDTO> silos;

        @ApiModelProperty(value = "物料等级列表")
        private List<MaterialGradeDTO> materialGrades;
    }

    /**
     * 筒仓信息
     */
    @Data
    @ApiModel(description = "筒仓信息")
    public static class SiloDTO {
        @ApiModelProperty(value = "筒仓编号")
        private String siloNumber;

        @ApiModelProperty(value = "筒仓名称")
        private String siloName;

        @ApiModelProperty(value = "物料代码")
        private String materialCode;

        @ApiModelProperty(value = "当前库存(KG)")
        private BigDecimal currentStock;

        @ApiModelProperty(value = "安全库存(KG)")
        private BigDecimal safetyStock;

        @ApiModelProperty(value = "最大容量(KG)")
        private BigDecimal maxCapacity;

        @ApiModelProperty(value = "批次信息")
        private String batchNumber;

        @ApiModelProperty(value = "产地")
        private String origin;
    }

    /**
     * 车间信息
     */
    @Data
    @ApiModel(description = "车间信息")
    public static class WorkshopDTO {
        @ApiModelProperty(value = "车间编号")
        private String workshopNumber;

        @ApiModelProperty(value = "车间名称")
        private String workshopName;

        @ApiModelProperty(value = "物料代码")
        private String materialCode;

        @ApiModelProperty(value = "当前库存(KG)")
        private BigDecimal currentStock;

        @ApiModelProperty(value = "批次信息")
        private String batchNumber;

        @ApiModelProperty(value = "产地")
        private String origin;
    }
}
