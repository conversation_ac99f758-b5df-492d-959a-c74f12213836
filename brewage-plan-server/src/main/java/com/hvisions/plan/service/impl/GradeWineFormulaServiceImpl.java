package com.hvisions.plan.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.hvisions.plan.consts.CommonConsts;
import com.hvisions.plan.entity.Formula;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.dao.GradeWineFormulaMapper;
import com.hvisions.plan.dto.GradeWineFormulaReq;
import com.hvisions.plan.entity.GradeWineFormula;
import com.hvisions.plan.entity.GwfDetail;
import com.hvisions.plan.service.IGradeWineFormulaService;
import com.hvisions.plan.service.IGwfDetailService;
import com.hvisions.plan.utils.CopyUtil;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 等级酒配方 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Service
public class GradeWineFormulaServiceImpl extends ServiceImpl<GradeWineFormulaMapper, GradeWineFormula> implements IGradeWineFormulaService {
    @Autowired
    IGwfDetailService gwfDetailService;
    @Autowired
    GradeWineFormulaMapper gradeWineFormulaMapper;

    /**
     * 新增等级酒配方
     *
     * @param req
     * @return
     */
    @Override
    public void addInfo(GradeWineFormulaReq req) {
        GradeWineFormula entity = CopyUtil.simpleCopy(req, GradeWineFormula.class);
        entity.setVersion(CommonConsts.INTEGER_NULL);
        entity.setState(StateEnum.NEW.getCode());
        save(entity);

        // 新增明细
        req.getGwfDetailReqList().forEach(it -> {
            GwfDetail gwfDetail = CopyUtil.simpleCopy(it, GwfDetail.class);
            gwfDetail.setGradeWineFormulaId(entity.getId());
            gwfDetail.setRatio(gwfDetail.getRatio().divide(new BigDecimal("100")));
            gwfDetailService.save(gwfDetail);
        });
    }

    /**
     * 修改等级酒配方
     *
     * @param req
     * @return
     */
    @Override
    public void updateInfo(GradeWineFormulaReq req) {
        GradeWineFormula gradeWineFormula = getById(req.getId());
        Assert.isTrue(Objects.equals(gradeWineFormula.getState(), StateEnum.NEW.getCode()), "只有【新建】状态下的数据才可以修改");

        GradeWineFormula entity = CopyUtil.simpleCopy(req, GradeWineFormula.class);
        updateById(entity);

        // 明细 先删后插
        gwfDetailService.remove(new LambdaQueryWrapper<GwfDetail>()
                .eq(GwfDetail::getGradeWineFormulaId, entity.getId()));
        req.getGwfDetailReqList().forEach(it -> {
            GwfDetail gwfDetail = CopyUtil.simpleCopy(it, GwfDetail.class);
            gwfDetail.setGradeWineFormulaId(entity.getId());
            gwfDetail.setRatio(gwfDetail.getRatio().divide(new BigDecimal("100")));
            gwfDetailService.save(gwfDetail);
        });
    }

    /**
     * 删除等级酒配方
     *
     * @param id
     * @return
     */
    @Override
    public void deleteInfo(Integer id) {
        GradeWineFormula gradeWineFormula = getById(id);
        Assert.isTrue(Objects.equals(gradeWineFormula.getState(), StateEnum.NEW.getCode()), "只有【新建】状态下的数据才可以删除");

        removeById(id);
        gwfDetailService.remove(new LambdaQueryWrapper<GwfDetail>()
                .eq(GwfDetail::getGradeWineFormulaId, id));
    }

    /**
     * 状态切换（生效/归档）
     *
     * @param id
     * @param status
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/24 15:52
     */
    @Override
    public void switchStatus(Integer id, String status) {
        GradeWineFormula entity = getById(id);

        if (Objects.equals(StateEnum.TAKE_EFFECT.getCode(), status)) {
            Assert.isTrue(Objects.equals(entity.getState(), StateEnum.NEW.getCode()), "当前记录非新建状态，不允许生效操作");
            if(entity.getProductionBaseName().contains("黄舣")){
                // 查询生产基地 记录
                LambdaQueryWrapper<GradeWineFormula> wrapper = new LambdaQueryWrapper<GradeWineFormula>()
                        .eq(GradeWineFormula::getProductionBaseId, entity.getProductionBaseId())
                        .orderByDesc(GradeWineFormula::getVersion);
                List<GradeWineFormula> GradeWineFormulaList = list(wrapper);
                entity.setVersion(GradeWineFormulaList.get(0).getVersion() + 1);
                entity.setEffectTime(LocalDateTime.now());
                // 将其他的条目置为归档
                List<GradeWineFormula> collect = GradeWineFormulaList.stream()
                        .filter(it -> it.getState().equals(StateEnum.TAKE_EFFECT.getCode())).collect(Collectors.toList());
                for (GradeWineFormula gradeWineFormula : collect) {
                    gradeWineFormula.setState(StateEnum.FILE.getCode());
                    updateById(gradeWineFormula);
                }
            }else{
                //非遗、罗汉生效条件改为基地+中心进行校验生效
                List<String> centerCodes = Optional.ofNullable(entity.getCenterCode())
                        .map(it -> Arrays.stream(it.split(","))
                                .filter(Strings::isNotBlank)
                                .map(String::toString)
                                .collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());
                Set<GradeWineFormula> list = new HashSet<GradeWineFormula>();
                centerCodes.forEach(item->{
                    //生产基地 糟源代码
                    LambdaQueryWrapper<GradeWineFormula> queryWrapper = new LambdaQueryWrapper<GradeWineFormula>()
                            .eq(GradeWineFormula::getProductionBaseId, entity.getProductionBaseId())
                            //.like(Formula::getCenters, item)
                            .apply( "FIND_IN_SET ('" + item + "',center_code)")
                            .eq(GradeWineFormula::getState,StateEnum.TAKE_EFFECT.getCode());
                    List<GradeWineFormula> formulaList = gradeWineFormulaMapper.selectList(queryWrapper);
                    formulaList.forEach(item1->{
                        list.add(item1);
                    });
                });
                //处理版本并生效
                Integer currentVersion = Optional.of(list)
                        .flatMap(it -> it.stream()
                                .filter(c -> Objects.nonNull(c.getVersion()))
                                .max(Comparator.comparing(GradeWineFormula::getVersion)))
                        .map(GradeWineFormula::getVersion)
                        .orElse(0);
                entity.setVersion(currentVersion + 1);
                entity.setEffectTime(LocalDateTime.now());
                // 将其他的条目置为归档
                List<GradeWineFormula> collect = list.stream()
                        .filter(it -> it.getState().equals(StateEnum.TAKE_EFFECT.getCode())).collect(Collectors.toList());
                for (GradeWineFormula gradeWineFormula : collect) {
                    gradeWineFormula.setState(StateEnum.FILE.getCode());
                    updateById(gradeWineFormula);
                }
            }
        } else {
            Assert.isTrue(Objects.equals(entity.getState(), StateEnum.TAKE_EFFECT.getCode()), "当前记录非生效状态，不允许存档操作");
        }
        entity.setState(status);
        updateById(entity);
    }

    /**
     * 复制
     *
     * @param id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/3/24 16:48
     */
    @Override
    public void copyInfo(Integer id) {
        GradeWineFormula entity = getById(id);
        GradeWineFormula gradeWineFormula = CopyUtil.simpleCopy(entity, GradeWineFormula.class);
        gradeWineFormula.setVersion(CommonConsts.INTEGER_NULL);
        gradeWineFormula.setState(StateEnum.NEW.getCode());
        save(gradeWineFormula);

        List<GwfDetail> gwfDetailList = gwfDetailService.list(new LambdaQueryWrapper<GwfDetail>()
                .eq(GwfDetail::getGradeWineFormulaId, id));
        gwfDetailList.forEach(it -> {
            GwfDetail gwfDetail = CopyUtil.simpleCopy(it, GwfDetail.class);
            gwfDetail.setGradeWineFormulaId(gradeWineFormula.getId());
            gwfDetailService.save(gwfDetail);
        });
    }
}
