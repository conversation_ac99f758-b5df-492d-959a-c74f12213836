package com.hvisions.plan.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.dto.PageInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.equipmentmsd.dto.location.LocationDTO;
import com.hvisions.plan.consts.ApiMessage;
import com.hvisions.plan.dao.*;
import com.hvisions.plan.dto.SchedulingTemplateQueryReq;
import com.hvisions.plan.dto.SchedulingTemplateReq;
import com.hvisions.plan.entity.*;
import com.hvisions.plan.enums.StateEnum;
import com.hvisions.plan.operlog.utils.StringUtils;
import com.hvisions.plan.service.IRowService;
import com.hvisions.plan.service.ISchedulingTemplateService;
import com.hvisions.plan.utils.CopyUtil;
import com.hvisions.plan.vo.SchedulingTemplateDetailVO;
import com.hvisions.plan.vo.SchedulingTemplateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * <p>
 * 排程模版 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@Service
@Slf4j
public class SchedulingTemplateServiceImpl extends ServiceImpl<SchedulingTemplateMapper, SchedulingTemplate> implements ISchedulingTemplateService {

    @Autowired
    SchedulingTemplateMapper schedulingTemplateMapper;
    @Autowired
    RetortContainerMapper retortContainerMapper;
    @Autowired
    CleaningTimeMapper cleaningTimeMapper;
    @Autowired
    ZengkouAlgorithmMapper zengkouAlgorithmMapper;
    @Autowired
    SchedulingMapper schedulingMapper;
    @Autowired
    RowMapper rowMapper;
    @Autowired
    IRowService rowService;

    @Override
    public Page<SchedulingTemplateVO> pageSchedule(SchedulingTemplateQueryReq req, PageInfo pageInfo) {
        Page<SchedulingTemplateVO> infoReturn = new Page<>(pageInfo.getPage(), pageInfo.getPageSize());
        log.error("[SchedulingTemplateServiceImpl][pageSchedule]排程模版列表==={}",req);
        List<SchedulingTemplateVO> schedulingTemplates = schedulingTemplateMapper.listSchedulingTemplate(infoReturn, req);
        if(schedulingTemplates.size()>0){
            schedulingTemplates.forEach(temp->{
                List<String> zengkouType = new ArrayList<String>();

                Integer id = temp.getId();
                String vinasseType = temp.getVinasseType();
                //甑口类型
                List<String> ids = StringUtils.str2List(vinasseType,",",true,true);
                ids.forEach(item->{
                    RetortContainer retortContainer = retortContainerMapper.selectById(item);
                    String zengkou = retortContainer.getSourceCode()+"("+retortContainer.getRetortCount()+")";
                    zengkouType.add(zengkou);
                });
                temp.setZengkouType(zengkouType);
                log.error("[SchedulingTemplateServiceImpl][pageSchedule]甑口类型==={}",zengkouType);
                //甑口算法
                LambdaQueryWrapper<ZengkouAlgorithm> wrapper2 = new LambdaQueryWrapper<ZengkouAlgorithm>()
                        .eq(ZengkouAlgorithm::getSchedulingTemplateId, id)
                        .eq(ZengkouAlgorithm::getDeleted, 0);
                List<ZengkouAlgorithm> zengkouAlgorithm = zengkouAlgorithmMapper.selectList(wrapper2);
                temp.setZengkouAlgorithm(zengkouAlgorithm);
                log.error("[SchedulingTemplateServiceImpl][pageSchedule]甑口算法==={}",zengkouAlgorithm);
                //打扫时间
                LambdaQueryWrapper<CleaningTime> wrapper = new LambdaQueryWrapper<CleaningTime>()
                        .eq(CleaningTime::getSchedulingTemplateId, id)
                        .eq(CleaningTime::getDeleted, 0);
                List<CleaningTime> cleaningTimes = cleaningTimeMapper.selectList(wrapper);
                temp.setCleaningTime(cleaningTimes);
                log.error("[SchedulingTemplateServiceImpl][pageSchedule]打扫时间==={}",cleaningTimes);
                //生产排次
                LambdaQueryWrapper<Row> wrapper1 = new LambdaQueryWrapper<Row>()
                        .eq(Row::getSchedulingTemplateId, id)
                        .eq(Row::getDeleted, 0)
                        .orderByAsc(Row::getRowSequence);
                List<Row> infos = rowMapper.selectList(wrapper1);
                temp.setRankingInformation(infos);
                log.error("[SchedulingTemplateServiceImpl][pageSchedule]生产排次==={}",infos);
            });
        }
        infoReturn.setRecords(schedulingTemplates);
        if (0 == schedulingTemplates.size()) {
            return infoReturn;
        }
        return infoReturn;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer handleNewSchedule(SchedulingTemplateReq schedulingReq) {
        log.error("[SchedulingTemplateServiceImpl][handleNewSchedule]新增排程模版==={}",schedulingReq);
        if(schedulingReq.getCleaningTimes() == null || schedulingReq.getCleaningTimes().size() <= 0){
            throw new BaseKnownException(10000, "打扫时间信息不能为空");
        }
        if(schedulingReq.getRankingInformations() == null || schedulingReq.getRankingInformations().size() <= 0){
            throw new BaseKnownException(10000, "生产排次信息不能为空");
        }
        if(schedulingReq.getZengkouAlgorithms() == null || schedulingReq.getZengkouAlgorithms().size() <= 0){
            throw new BaseKnownException(10000, "甑口算法信息不能为空");
        }
        SchedulingTemplate schedulingTemplate = new SchedulingTemplate();
        //String vinasseType = String.join(",", schedulingReq.getVinasseType().toString()).substring(0,schedulingReq.getVinasseType().size()-1);
        schedulingTemplate.setState(StateEnum.NEW.getCode());
        schedulingTemplate.setProductionbaseId(schedulingReq.getProductionbaseId());
        schedulingTemplate.setVinasseType(schedulingReq.getVinasseType());
        int index = schedulingTemplateMapper.insert(schedulingTemplate);
        //打扫时间
        if(schedulingReq.getCleaningTimes().size()>0){
            schedulingReq.getCleaningTimes().forEach(item->{
                CleaningTime cleaningTime = new CleaningTime();
                cleaningTime.setSymbol(item.getSymbol());
                cleaningTime.setMaintenanceDays(item.getMaintenanceDays());
                cleaningTime.setCleaningDays(item.getCleaningDays());
                cleaningTime.setSchedulingTemplateId(schedulingTemplate.getId());
                cleaningTimeMapper.insert(cleaningTime);
            });
        }
        //生产排次
        if(schedulingReq.getRankingInformations().size()>0){
            schedulingReq.getRankingInformations().forEach(item->{
                LocationDTO dto = schedulingMapper.getLocationById(schedulingReq.getProductionbaseId());
                Row row = new Row();
                //row.setId(item.getId());
                row.setProductionBaseId(schedulingReq.getProductionbaseId());
                row.setProductionBaseName(dto.getName());
                row.setName(item.getName());
                row.setTotalDays(Integer.valueOf(item.getTotalDays()));
                row.setVersion(1);
                row.setRowSequence(item.getRowSequence());
                //row.setEffectTime(LocalDateTime.now());
                row.setCheckState(item.getCheckState());
                row.setState(StateEnum.NEW.getCode());
                row.setSchedulingTemplateId(schedulingTemplate.getId());
                rowMapper.insert(row);
            });
        }
        //甑口算法
        if(schedulingReq.getZengkouAlgorithms().size()>0){
            schedulingReq.getZengkouAlgorithms().forEach(item->{
                ZengkouAlgorithm zengkouAlgorithm = new ZengkouAlgorithm();
                zengkouAlgorithm.setZengkouName(item.getZengkouName());
                zengkouAlgorithm.setRelatedAlgorithms(item.getRelatedAlgorithms());
                zengkouAlgorithm.setSchedulingTemplateId(schedulingTemplate.getId());
                zengkouAlgorithmMapper.insert(zengkouAlgorithm);
            });
        }
        return schedulingTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void validData(Integer id) {
        SchedulingTemplate scheduling = schedulingTemplateMapper.selectById(id);
        log.error("[SchedulingTemplateServiceImpl][validData]排程模版生效==={}",scheduling);
        //生效操作后，生效时需要根据生产基地做唯一校验，如果有相同已生效数据，则将已生效数据状态改为存档，生效操作完成，根据操作时间插入生效日期。
        LambdaQueryWrapper<SchedulingTemplate> queryWrapper = new LambdaQueryWrapper<SchedulingTemplate>()
                .eq(SchedulingTemplate::getProductionbaseId, scheduling.getProductionbaseId())
                .eq(SchedulingTemplate::getState, StateEnum.TAKE_EFFECT.getCode())
                .eq(SchedulingTemplate::getDeleted, 0);
        SchedulingTemplate schedulingTemplate = schedulingTemplateMapper.selectOne(queryWrapper);
        if(schedulingTemplate != null){
            schedulingTemplate.setState(StateEnum.FILE.getCode());
            schedulingTemplateMapper.updateById(schedulingTemplate);
        }
        scheduling.setState(StateEnum.TAKE_EFFECT.getCode());
        scheduling.setEffectiveTime(new Date());
        this.updateById(scheduling);

        //生产排次
        LambdaQueryWrapper<Row> wrapper1 = new LambdaQueryWrapper<Row>()
                .eq(Row::getSchedulingTemplateId, scheduling.getId())
                .eq(Row::getDeleted, 0)
                .orderByAsc(Row::getRowSequence);
        List<Row> infos = rowMapper.selectList(wrapper1);
        infos.forEach(item->{
            rowService.validData(item.getId());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invalidData(Integer id) {
        SchedulingTemplate scheduling = schedulingTemplateMapper.selectById(id);
        log.error("[SchedulingTemplateServiceImpl][invalidData]排程模版归档==={}",scheduling);
        Assert.isTrue(StateEnum.TAKE_EFFECT.getCode().equals(scheduling.getState()), "当前排程模版非生效状态，不允许进行归档操作");
        scheduling.setState(StateEnum.FILE.getCode());
        schedulingTemplateMapper.updateById(scheduling);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String delete(Integer id) {
        SchedulingTemplate schedulingTemplate = schedulingTemplateMapper.selectById(id);
        log.error("[SchedulingTemplateServiceImpl][delete]排程模版删除==={}",schedulingTemplate);
        Assert.isTrue(StateEnum.NEW.getCode().equals(schedulingTemplate.getState()), "当前排程模版非新建状态，不允许进行删除操作");
        //删除排程模版相关数据
        schedulingTemplateMapper.deleteById(id);
        LambdaQueryWrapper<CleaningTime> wrapper = new LambdaQueryWrapper<CleaningTime>()
                .eq(CleaningTime::getSchedulingTemplateId, id);
        cleaningTimeMapper.delete(wrapper);
        LambdaQueryWrapper<Row> wrapper1 = new LambdaQueryWrapper<Row>()
                .eq(Row::getSchedulingTemplateId, id);
        List<Row> infos = rowMapper.selectList(wrapper1);
        if(infos.size()>0){
            infos.forEach(item->{
                item.setState(StateEnum.FILE.getCode());
                item.setDeleted(true);
                rowMapper.updateById(item);
            });
        }
        //rowMapper.delete(wrapper1);
        LambdaQueryWrapper<ZengkouAlgorithm> wrapper2 = new LambdaQueryWrapper<ZengkouAlgorithm>()
                .eq(ZengkouAlgorithm::getSchedulingTemplateId, id);
        zengkouAlgorithmMapper.delete(wrapper2);
        return ApiMessage.DELETE_SUCCESS;
    }

    @Override
    public SchedulingTemplateDetailVO detail(Integer id) {
        SchedulingTemplateDetailVO schedulingTemplateDetailVO = new SchedulingTemplateDetailVO();
        SchedulingTemplate schedulingTemplate = schedulingTemplateMapper.selectById(id);
        schedulingTemplateDetailVO = CopyUtil.simpleCopy(schedulingTemplate, SchedulingTemplateDetailVO.class);
        List<String> ids = StringUtils.str2List(schedulingTemplateDetailVO.getVinasseType(),",",true,true);
        schedulingTemplateDetailVO.setZengkouType(ids);
        //打扫时间
        LambdaQueryWrapper<CleaningTime> wrapper = new LambdaQueryWrapper<CleaningTime>()
                .eq(CleaningTime::getSchedulingTemplateId, id)
                .eq(CleaningTime::getDeleted, 0);
        List<CleaningTime> cleaningTimes = cleaningTimeMapper.selectList(wrapper);
        schedulingTemplateDetailVO.setCleaningTime(cleaningTimes);
        log.error("[SchedulingTemplateServiceImpl][detail]打扫时间==={}",cleaningTimes);
        //生产排次
        LambdaQueryWrapper<Row> wrapper1 = new LambdaQueryWrapper<Row>()
                .eq(Row::getSchedulingTemplateId, id)
                .eq(Row::getDeleted, 0)
                .orderByAsc(Row::getRowSequence);
        List<Row> infos = rowMapper.selectList(wrapper1);
        schedulingTemplateDetailVO.setRankingInformation(infos);
        log.error("[SchedulingTemplateServiceImpl][detail]生产排次==={}",infos);
        //甑口算法
        LambdaQueryWrapper<ZengkouAlgorithm> wrapper2 = new LambdaQueryWrapper<ZengkouAlgorithm>()
                .eq(ZengkouAlgorithm::getSchedulingTemplateId, id)
                .eq(ZengkouAlgorithm::getDeleted, 0);
        List<ZengkouAlgorithm> zengkouAlgorithm = zengkouAlgorithmMapper.selectList(wrapper2);
        schedulingTemplateDetailVO.setZengkouAlgorithms(zengkouAlgorithm);
        log.error("[SchedulingTemplateServiceImpl][detail]甑口算法==={}",zengkouAlgorithm);
        return schedulingTemplateDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateSchedule(SchedulingTemplateReq schedulingReq) {
        //不是新建状态的排程模版不允许编辑
        Integer oldScheduleId = schedulingReq.getId();
        SchedulingTemplate oldSchedule = schedulingTemplateMapper.selectById(oldScheduleId);
        Assert.isTrue(StateEnum.NEW.getCode().equals(oldSchedule.getState()), "当前排程模版非新建状态，不允许进行编辑操作");
        //干掉旧的排程模版相关数据
        schedulingTemplateMapper.deleteById(oldScheduleId);
        LambdaQueryWrapper<CleaningTime> wrapper = new LambdaQueryWrapper<CleaningTime>()
                .eq(CleaningTime::getSchedulingTemplateId, oldScheduleId);
        cleaningTimeMapper.delete(wrapper);
        LambdaQueryWrapper<Row> wrapper1 = new LambdaQueryWrapper<Row>()
                .eq(Row::getSchedulingTemplateId, oldScheduleId);
        List<Row> infos = rowMapper.selectList(wrapper1);
        if(infos.size()>0){
            infos.forEach(item->{
                item.setState(StateEnum.FILE.getCode());
                item.setDeleted(true);
                rowMapper.updateById(item);
            });
        }
        //rowMapper.delete(wrapper1);
        LambdaQueryWrapper<ZengkouAlgorithm> wrapper2 = new LambdaQueryWrapper<ZengkouAlgorithm>()
                .eq(ZengkouAlgorithm::getSchedulingTemplateId, oldScheduleId);
        zengkouAlgorithmMapper.delete(wrapper2);
        log.error("[SchedulingTemplateServiceImpl][updateSchedule]排程模版修改==={}",schedulingReq);
        //新增排程模版
        return this.handleNewSchedule(schedulingReq);
    }
}
