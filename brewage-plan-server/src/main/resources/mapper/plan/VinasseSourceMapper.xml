<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.plan.dao.VinasseSourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hvisions.plan.entity.VinasseSource">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator_id" property="creatorId" />
        <result column="updater_id" property="updaterId"/>
        <result column="site_num" property="siteNum" />
        <result column="deleted" property="deleted" />
        <result column="production_base_id" property="productionBaseId" />
        <result column="production_base_name" property="productionBaseName" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="type_code" property="typeCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        creator_id, updater_id, site_num, deleted, production_base_id, production_base_name, name, code, type_code
    </sql>

    <select id="selectBatchCodes" resultType="java.lang.Integer">
        select id from t_pp_vinasse_source where deleted = 0 and production_base_id = #{productionBaseId} and  code in
        <foreach collection="codeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectFormulaSourceId" resultType="java.lang.Integer">
        select b.id from t_pp_formula a
                            INNER JOIN t_pp_vinasse_source b on a.source_id = b.id
        where a.state = 1 and a.production_base_id = 1 and b.id = #{sourceId}

    </select>
</mapper>
