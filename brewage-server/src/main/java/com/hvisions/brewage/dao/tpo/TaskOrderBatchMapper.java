package com.hvisions.brewage.dao.tpo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hvisions.brewage.dto.tpo.TaskOrderBatchQueryDTO;import com.hvisions.brewage.entity.tpo.TaskOrderBatch;import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderSap.OrderUseByTypeDTO;import com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.BatchReturnTopVO;import com.hvisions.brewage.vo.tpo.TaskOrderBatchVO;import java.util.List;

public interface TaskOrderBatchMapper extends BaseMapper<TaskOrderBatch> {
    /**
     * 根据订单查询物料数据
     *
     * @param queryDTO
     * @return
     */
    List<TaskOrderBatchVO> selectListByOrder(TaskOrderBatchQueryDTO queryDTO);

    /**
     * 按投入类型查询订单使用的物料总量
     *
     * @param orderUseByTypeDTO
     * @return
     */
    List<BatchReturnTopVO> selectOrderUseByType(OrderUseByTypeDTO orderUseByTypeDTO);
}