package com.hvisions.brewage.mkwine.config;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.brewage.dao.base.ProdConfigItemMapper;
import com.hvisions.brewage.dto.inspectionPit.PitWineDTO;
import com.hvisions.brewage.entity.base.ProdConfigItem;
import com.hvisions.brewage.feign.plan.BrewagePlanClient;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoIncreaseAmplitudeDetailMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.TPoIncreaseAmplitudeMapper;
import com.hvisions.brewage.mkwine.dao.dayplan.WorkshopPitDayPlanMapper;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.AmplitudeDetailQueryDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.IncreaseAmplitudeDetailDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkMonthPlanDTO;
import com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan.WorkshopPitDayPlanDTO;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoIncreaseAmplitude;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoIncreaseAmplitudeDetail;
import com.hvisions.brewage.mkwine.entity.dayplan.TPoWorkshopPitDayPlan;
import com.hvisions.brewage.mkwine.req.MonthPlanMaterialReq;
import com.hvisions.brewage.mkwine.service.WorkshopPitDayPlanService;
import com.hvisions.brewage.mkwine.vo.MonthPlanDetailVO;
import com.hvisions.brewage.mkwine.vo.MonthPlanMaterialVO;
import com.hvisions.brewage.mkwine.vo.MonthPlanVinasseVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthDetailVO;
import com.hvisions.brewage.mkwine.vo.dayplan.WorkshopPitDayPlan.WorkMonthVO;
import com.hvisions.brewage.mkwine.vo.dayplan.task.ProductionSchedule;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 近期涨幅定时任务
 */
@Slf4j
@Component
@EnableScheduling
public class IncreaseAmplitudeTask {

    @Resource
    TPoIncreaseAmplitudeDetailMapper increaseAmplitudeDetailMapper;

    @Resource
    WorkshopPitDayPlanService workshopPitDayPlanService;

    @Resource
    TPoIncreaseAmplitudeMapper increaseAmplitudeMapper;

    @Resource
    BrewagePlanClient brewagePlanClient;

    @Resource
    ProdConfigItemMapper prodConfigItemMapper;

    @Resource
    WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    WorkshopPitDayPlanMapper workshopPitDayPlanMapper;

    @Resource
    WorkshopPitOrderPotTaskMapper orderPotTaskMapper;

    /**
     * 近期涨幅定时任务
     */
    @Scheduled(cron = "0 5 0 * * ?")
    @SchedulerLock(name = "increaseAmplitude")
    public void increaseAmplitude() {
        log.info("开始生成近期涨幅数据");
        //获取配置项
        List<ProdConfigItem> configTime = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemName, "涨幅定时时间").eq(ProdConfigItem::getDeleted, false));
        List<ProdConfigItem> configCycle = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemName, "涨幅计算周期").eq(ProdConfigItem::getDeleted, false));
        for (ProdConfigItem prodConfigItem : configTime) {
            //遍历配置时间
            Integer locationId = Integer.valueOf(prodConfigItem.getWorkshop());
            //判断配置时间是否生成

            //遍历是否有周期配置
            if (configCycle.stream().noneMatch(item -> item.getWorkshop().equals(prodConfigItem.getWorkshop()))) {
                continue;
            }
            //获取 配置项（天） 前的数据
            Date date = DateUtil.addDay(new Date(), -7);
            AmplitudeDetailQueryDTO queryDTO = new AmplitudeDetailQueryDTO();
            queryDTO.setInitiationTime(date);
            queryDTO.setLocationId(locationId);
            List<IncreaseAmplitudeDetailDTO> list = increaseAmplitudeDetailMapper.getIncreaseAmplitudeDetailPage(queryDTO);
            //按照车间分组
            if (CollectionUtils.isNotEmpty(list)) {
                TPoIncreaseAmplitude amplitude = new TPoIncreaseAmplitude();
                BeanUtils.copyProperties(list.get(0), amplitude);
                amplitude.setStartTime(list.stream().filter(Objects::nonNull).map(IncreaseAmplitudeDetailDTO::getOpenPitFinishTime).filter(Objects::nonNull).min(Date::compareTo).orElse(null));
                amplitude.setEndTime(list.stream().filter(Objects::nonNull).map(IncreaseAmplitudeDetailDTO::getOpenPitFinishTime).filter(Objects::nonNull).max(Date::compareTo).orElse(null));
                amplitude.setInPutNum(list.stream().mapToInt(IncreaseAmplitudeDetailDTO::getInPitNum).sum());
                amplitude.setOutPutNum(list.stream().mapToInt(IncreaseAmplitudeDetailDTO::getOutPitNum).sum());
                //涨幅日期
                amplitude.setIncreaseDate(DateUtil.getTodayStartDate());
                amplitude.setOrderNum(list.size());
                //设置涨幅 近期涨幅=（蒸馏总甑口-在制总甑口）/在制总甑口*100%
                BigDecimal increaseAmplitude = new BigDecimal(amplitude.getOutPutNum()).subtract(new BigDecimal(amplitude.getInPutNum())).divide(new BigDecimal(amplitude.getInPutNum()), 2, RoundingMode.HALF_UP);
                amplitude.setIncreaseAmplitude(increaseAmplitude);
                increaseAmplitudeMapper.insert(amplitude);
                //生成明细数据
                for (IncreaseAmplitudeDetailDTO increaseAmplitudeDetailDTO : list) {
                    TPoIncreaseAmplitudeDetail detail = new TPoIncreaseAmplitudeDetail();
                    BeanUtils.copyProperties(increaseAmplitudeDetailDTO, detail);
                    detail.setIncreaseAmplitudeId(amplitude.getId());
                    increaseAmplitudeDetailMapper.insert(detail);
                }
            }
        }
        log.info("生成近期涨幅数据执行结束");
    }

    /**
     * 生成日计划
     */
    @Scheduled(cron = "0 0 16 * * ?")
    @SchedulerLock(name = "createDayPlan")
    public void createDayPlan() {
        log.info("自动生成日计划，开始执行");
        //获取日计划糟源排班规则
        //List<ProdConfigItem> dayPlanVinasseList = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().like(ProdConfigItem::getItemCode, "day_plan_vinasse_"));
        //1）单甑生产时长：每类糟源单甑生产时长；
        List<ProdConfigItem> prodTimeAll = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().like(ProdConfigItem::getItemCode, "prod_time"));
        List<ProdConfigItem> prodTimeList = prodTimeAll.stream().filter(i -> "5".equals(i.getWorkshop())).collect(Collectors.toList());
        //2）每日生产产能：每日生产甑口能力（CJ）；
        List<ProdConfigItem> dayCapacityAll = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().like(ProdConfigItem::getItemCode, "day_capacity"));
        List<ProdConfigItem> dayCapacityList = prodTimeAll.stream().filter(i -> "5".equals(i.getWorkshop())).collect(Collectors.toList());
        //Integer dayCapacity = Integer.valueOf(workShopConfig.stream().filter(i -> "day_capacity".equals(i.getItemCode())).map(ProdConfigItem::getItemValue).findFirst().orElse("60"));
        //3）空窖数量：生产期间车间空窖数量；
        List<ProdConfigItem> emptyPitNumAll = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemCode, "empty_pit_num"));
        int emptyPitNum = Integer.parseInt(emptyPitNumAll.stream().filter(i -> "5".equals(i.getWorkshop())).map(ProdConfigItem::getItemValue).findFirst().orElse("3"));
        //单窖入窖甑口：每口窖池可入窖的蒸馏甑口18
        List<ProdConfigItem> pitIntoNumAll = prodConfigItemMapper.selectList(new LambdaUpdateWrapper<ProdConfigItem>().eq(ProdConfigItem::getItemCode, "pit_into_num"));
        Integer pitIntoNum = Integer.valueOf(pitIntoNumAll.stream().filter(i -> "5".equals(i.getWorkshop())).map(ProdConfigItem::getItemValue).findFirst().orElse("18"));
        //剩余生产排班物料
        WorkMonthVO workMonth = getWorkMonth(prodTimeList, 2, 5);
        //月开始时间
        LocalDate monthBeginDate = workMonth.getMonthBeginDate();
        //月截止时间
        LocalDate monthEndDate = workMonth.getMonthEndDate();
        //当日排班计划时间
        String planDate = workMonth.getPlanDate();
        //今天不能生产完的日计划
        List<TPoWorkshopPitDayPlan> continueDayPlans = workMonth.getContinueDayPlans();
        List<ProductionSchedule> productionSchedules = new ArrayList<>();
        //当天未做完的日计划
        if (CollectionUtils.isNotEmpty(continueDayPlans)) {
            //先将之前生产的数据进行排产
            List<Integer> continueIds = continueDayPlans.stream().map(TPoWorkshopPitDayPlan::getOrderId).collect(Collectors.toList());
            productionSchedules = workshopPitOrderMapper.selectOrderProductionSchedule(2, 5, null, continueIds);
            for (ProductionSchedule productionSchedule : productionSchedules) {
                productionSchedule.setRemainNum(continueDayPlans.stream().filter(p -> p.getOrderId().equals(productionSchedule.getId())).mapToInt(TPoWorkshopPitDayPlan::getBalancePotCount).sum());
            }
        }
        //获取近期涨幅
        TPoIncreaseAmplitude amplitude = increaseAmplitudeMapper.selectOne(new LambdaQueryWrapper<TPoIncreaseAmplitude>().eq(TPoIncreaseAmplitude::getCenterId, 2).eq(TPoIncreaseAmplitude::getLocationId, 5).orderByDesc(TPoIncreaseAmplitude::getId).last("limit 1"));
        BigDecimal increaseAmplitude = amplitude.getIncreaseAmplitude();
        List<WorkshopPitDayPlanDTO> addList = new ArrayList<>();
        //排产空窖的情况 这里需要的是所在窖池的所有订单都是丢糟订单数据
        //获取能够进行空窖排产的ZL、DZ、HZ、MZ订单
        List<ProductionSchedule> emptyProductionSchedules = workshopPitOrderMapper.selectOrderProductionSchedule(2, 5, workMonth.getEmptyDayPlanVinasseList(), null);
        //如果是排班的月份的第一天，则制作空窖排产需求
        if (planDate.equals(monthBeginDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))){
            //1.空窖排产 - 开工排产
            //1.1 获取空窖的排产窖池，将这里的窖池获取出来，按照设置的空窖数量进行排产
            List<String> fullPit = emptyProductionSchedules.stream().map(ProductionSchedule::getFullPitId).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(fullPit) || fullPit.size() < emptyPitNum) {
                log.error("空窖排产失败,当前可分派窖池订单不足,当前可以排窖池数量{},需要空窖排数量{}", CollectionUtils.isEmpty(fullPit)?0:fullPit.size(), emptyPitNum);
                return;
            }
            //1.2 对窖池的所有订单进行汇总丢糟排产
            List<ProductionSchedule> scheduleList = new ArrayList<>();
            for (int i=0; i<emptyPitNum; i++){
                //本轮需要排产的窖池
                int finalI = i;
                scheduleList.addAll(emptyProductionSchedules.stream().filter(s -> fullPit.get(finalI).equals(s.getFullPitId())).collect(Collectors.toList()));
            }

            List<String> vinasseList = scheduleList.stream().map(ProductionSchedule::getVinasseName).distinct().collect(Collectors.toList());
            //1.3 按照不通的糟源情况区分进行计算。如果一种糟源则采用每日产能计算，如果多种糟源则用单甑时长计算
            if (vinasseList.size() == 1) {
                //1.3.1 同种糟源进行生成数据
                Integer dayCapacity = Integer.valueOf(dayCapacityAll.stream().filter(c -> c.getItemCode().equals(vinasseList.get(0))).map(ProdConfigItem::getItemValue).findFirst().orElse("72"));
                addList = arrangeByDayCapacity(dayCapacity, scheduleList, increaseAmplitude, planDate);
            } else {
                //1.3.2 不同糟源生成数据
                addList = arrangeByProdTime(prodTimeList, scheduleList, increaseAmplitude, planDate, 1440);
            }
        } else if (planDate.equals(monthEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))){
            //如果是排班的最后一天。保证当日窖池无空窖
            //获取当前的空窖和正在执行的开窖窖池，分配投粮的窖池
            List<ProductionSchedule> productionSchedules2 = workshopPitOrderMapper.selectOrderProductionSchedule(2, 5, workMonth.getProdDayPlanVinasseList(), null);
            prodDayPlanSchedule(productionSchedules2, increaseAmplitude, prodTimeList, 1440, addList, planDate);
        } else {
            //2.生产排产 - 持续型排产期
            //判断月计划回酒使用情况，如果使用的回酒已经超过了，则不在使用丢糟排产
            Boolean flag = checkHjFlag(monthBeginDate, monthEndDate, 2, 5, workMonth.getHjPlanQuality());
            //判断当日排产情况
            if (workMonth.getIsEmptyProd() && flag) {
                //判断当前空窖数量，根据空窖数量确认还需要进行的丢糟排产
                if(workMonth.getPlanEmptyNum() >= emptyPitNum) {
                    //空窖余量比设置的数量大的情况。直接进行投粮排产
                    prodDayPlanSchedule(emptyProductionSchedules, increaseAmplitude, prodTimeList, 1440, addList, planDate);
                } else {
                    //先进行空窖排产，如果排产完还有时间进行投粮排产
                    int count = (int) continueDayPlans.stream().map(TPoWorkshopPitDayPlan::getPitId).distinct().count();
                    //1.先进行空窖排产
                    for (int i=0; i<count; i++) {
                        addList.add(initDayPlan(productionSchedules.get(i), productionSchedules.get(i).getRemainNum(), 0, planDate));
                    }
                    // 统计耗时
                    int sum = addList.stream().mapToInt(WorkshopPitDayPlanDTO::getPlanOutPotCount).sum();
                    Integer prodTime = Integer.valueOf(prodTimeList.stream()
                            .filter(p -> continueDayPlans.get(0).getVinasseCode().equals(p.getItemCode()))
                            .findFirst()
                            .map(ProdConfigItem::getItemValue).orElse("25"));
                    List<ProductionSchedule> productionSchedules2 = workshopPitOrderMapper.selectOrderProductionSchedule(2, 5, workMonth.getProdDayPlanVinasseList(), null);
                    prodDayPlanSchedule(productionSchedules2, increaseAmplitude, prodTimeList, 1440 - sum*prodTime, addList, planDate);
                }
            } else {
                //投粮排产
                //全天的生产时长
                int allMinute = 1440;
                //2.1获取能够进行空窖排产的CJ、CB订单
                List<ProductionSchedule> productionSchedules2 = workshopPitOrderMapper.selectOrderProductionSchedule(2, 5, workMonth.getProdDayPlanVinasseList(), null);
                productionSchedules.addAll(productionSchedules2);
                prodDayPlanSchedule(productionSchedules, increaseAmplitude, prodTimeList, allMinute, addList, planDate);
//            if ((ChronoUnit.DAYS.between(DateUtil.getLocalDate(planDate), monthEndDate) < 7)){
//                //如果是停产前一个礼拜，则表示最后一天的排班日期已经确定，排除最后一天，需要预留足够的窖池做入窖这6天排班可以适当浮动
//            }
            }
        }
        try {
            workshopPitDayPlanService.addWorkshopPitDayPlan(addList);
        } catch (Exception e) {
            log.error("日计划自动排产失败:" + e.getMessage());
        }
        log.info("自动生成日计划，执行结束");
    }

    /**
     * 投粮生产排日计划
     * @param productionSchedules
     * @param increaseAmplitude
     * @param prodTimeList
     * @param allMinute
     * @param addList
     * @param planDate
     */
    private void prodDayPlanSchedule(List<ProductionSchedule> productionSchedules, BigDecimal increaseAmplitude, List<ProdConfigItem> prodTimeList, int allMinute, List<WorkshopPitDayPlanDTO> addList, String planDate) {
        //根据窖池的空窖数量预估要开的订单
        //这里是需要出的甑口数量
//                int outputNum = workMonth.getPlanEmptyNum() * pitIntoNum;
        for (ProductionSchedule productionSchedule : productionSchedules) {
            //计算产能
            //根据涨幅计算出窖甑口
            int outNum = new BigDecimal(productionSchedule.getInPitNum()).multiply(increaseAmplitude).setScale(0, RoundingMode.UP).intValue();
            outNum = productionSchedule.getRemainNum() != null?productionSchedule.getRemainNum():outNum;
            Integer prodTime = Integer.valueOf(prodTimeList.stream().filter(t -> t.getItemCode().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            int prodMinute = allMinute * prodTime;
            if (prodMinute < allMinute) {
                //没有超过当天的生产时长
                allMinute = allMinute - prodMinute;
                addList.add(initDayPlan(productionSchedule, outNum, 0, planDate));
            } else {
                //当天剩余时长已经不足以生产完该订单,计算还能生产的订单
                Integer prodDay = new BigDecimal(allMinute).divide(new BigDecimal(prodTime), 0, RoundingMode.FLOOR).intValue();
                //剩余的数量不足的情况
                int orderRemainderCount = outNum - prodDay;
                addList.add(initDayPlan(productionSchedule, outNum, orderRemainderCount, planDate));
                break;
            }
        }
    }

    /**
     * 初始化日计划数据信息
     * @param productionSchedule
     * @param outNum
     * @param orderRemainderCount
     * @return
     */
    private WorkshopPitDayPlanDTO initDayPlan(ProductionSchedule productionSchedule, int outNum, int orderRemainderCount, String planDate) {
        WorkshopPitDayPlanDTO workshopPitDayPlanDTO = new WorkshopPitDayPlanDTO();
        workshopPitDayPlanDTO.setOrderId(productionSchedule.getId());
        workshopPitDayPlanDTO.setPitId(productionSchedule.getPitId());
        workshopPitDayPlanDTO.setWorkshopCenterId(productionSchedule.getCenterId());
        workshopPitDayPlanDTO.setWorkshopId(productionSchedule.getLocationId());
        workshopPitDayPlanDTO.setCategoryId(productionSchedule.getCategoryId());
        workshopPitDayPlanDTO.setPlanDate(DateUtil.parse(planDate, "yyyy-MM-dd"));
        workshopPitDayPlanDTO.setIsOpened(true);
        workshopPitDayPlanDTO.setCalcOutPotCount(outNum);
        workshopPitDayPlanDTO.setBalancePotCount(0);
        workshopPitDayPlanDTO.setPlanOutPotCount(outNum);
        workshopPitDayPlanDTO.setOrderRemainderCount(orderRemainderCount);
        return workshopPitDayPlanDTO;
    }

    /**
     * 根据排产周期去判断使用的回酒情况
     *
     * @param monthBeginDate
     * @param monthEndDate
     * @param centerId
     * @param locationId
     * @param hjPlanQuality
     * @return 判断标识是否通过
     */
    private Boolean checkHjFlag(LocalDate monthBeginDate, LocalDate monthEndDate, Integer centerId, Integer locationId, BigDecimal hjPlanQuality) {
        BigDecimal hjUseTotal = orderPotTaskMapper.selectHjUseTotal(monthBeginDate, monthEndDate, centerId, locationId);
        return hjPlanQuality.compareTo(hjUseTotal) > 0;
    }

    /**
     * 根据今天的实际生产情况去更新月计划需求量
     * @param prodTimeList
     * @param centerId
     * @param locationId
     * @return
     */
    private WorkMonthVO getWorkMonth(List<ProdConfigItem> prodTimeList, Integer centerId, Integer locationId) {
        //获取当前日期的下一天
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        // 获取年份
        int cycleYear = calendar.get(Calendar.YEAR);
        // 获取月份（注意：月份从0开始，0表示1月）
        int cycleMonth = calendar.get(Calendar.MONTH) + 1;
        //需要排班计划的日期
        String planDate = DateUtil.dateFormat(calendar.getTime(), "yyyy-MM-dd");
        //获取月计划排班情况
        //根据排班情况获取要排班的糟源
        WorkMonthPlanDTO workMonthPlanDTO = new WorkMonthPlanDTO();
        workMonthPlanDTO.setCenterId(2);
        workMonthPlanDTO.setLocationId(5);
        workMonthPlanDTO.setPlanTime(calendar.getTime().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate());
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算到次日凌晨的时间差（分钟） -- 今天还能生产的时间
        int minutesToMidnight = (int) (ChronoUnit.MINUTES.between(now.toLocalTime(),LocalTime.MAX) + 1);
        WorkMonthVO workMonthVO = workshopPitDayPlanService.getMonthlyPlanRemainingProductionRetort(workMonthPlanDTO);
        //开始日期
        LocalDate monthBeginDate = workMonthVO.getMonthBeginDate();
        //结束日期
        LocalDate monthEndDate = workMonthVO.getMonthEndDate();
        //预计空窖数量
        int planEmptyNum = 0;
        //物料需求
        List<WorkMonthDetailVO> workMonthDetailVOList = workMonthVO.getWorkMonthDetailVOList();
        //CZ的生产应该计划到CJ上面。因为CJ会转换成CZ，这里需要转换
        int CZCount = workMonthDetailVOList.stream().filter(d -> "CJ".equals(d.getVinasseCode())).mapToInt(WorkMonthDetailVO::getPotCount).sum();
        workMonthDetailVOList.stream()
                .filter(d -> "CJ".equals(d.getVinasseCode()))
                .findFirst()
                .ifPresent(d -> d.setPotCount(d.getPotCount() + CZCount));
        //未完成数量预留：假设排产时间为每日18:00，则当日18:00~24:00期间生产还在进行，排产时，需依据当天已生产数据完成率评估16:00~24:00期间可完成量，基于该数据再继续排产日次生产安排
        //获取当天的日计划 判断完成情况 -- 将正在执行的排在前面
        List<TPoWorkshopPitDayPlan> tPoWorkshopPitDayPlans = workshopPitDayPlanMapper.selectList(new LambdaUpdateWrapper<TPoWorkshopPitDayPlan>().eq(TPoWorkshopPitDayPlan::getWorkshopId, workMonthPlanDTO.getLocationId()).eq(TPoWorkshopPitDayPlan::getWorkshopCenterId, workMonthPlanDTO.getCenterId()).eq(TPoWorkshopPitDayPlan::getPlanDate, planDate).orderByDesc(TPoWorkshopPitDayPlan::getPlanStatus));
        for (TPoWorkshopPitDayPlan tPoWorkshopPitDayPlan : tPoWorkshopPitDayPlans) {
            if (5 == tPoWorkshopPitDayPlan.getPlanStatus() && tPoWorkshopPitDayPlan.getOrderRemainderCount() == null) {
                //如果没有剩余排产量并且已经完成。则不用单独计算
                planEmptyNum ++;
                continue;
            }
            Integer prodTime = Integer.valueOf(prodTimeList.stream().filter(t -> t.getItemCode().contains(tPoWorkshopPitDayPlan.getVinasseCode())).map(ProdConfigItem::getItemValue).findFirst().orElse("20"));
            if (minutesToMidnight - prodTime*tPoWorkshopPitDayPlan.getBalancePotCount() > 0) {
                //如果剩余时间足够生产的情况下
                minutesToMidnight = minutesToMidnight - prodTime*tPoWorkshopPitDayPlan.getBalancePotCount();
                //默认能生产完。需要扣除剩余的糟源数据
                workMonthDetailVOList.stream()
                        .filter(d -> tPoWorkshopPitDayPlan.getVinasseCode().equals(d.getVinasseCode()))
                        .findFirst()
                        .ifPresent(d -> d.setPotCount(d.getPotCount() - tPoWorkshopPitDayPlan.getBalancePotCount()));
                tPoWorkshopPitDayPlan.setBalancePotCount(0);
                planEmptyNum ++;
            } else {
                //剩余时间不够生产的情况，计算最后还能生产的甑口数据
                BigDecimal prodNum = new BigDecimal(minutesToMidnight).divide(new BigDecimal(prodTime), 0, RoundingMode.FLOOR);
                //今天不能生产的数据
                int balanceNum = tPoWorkshopPitDayPlan.getBalancePotCount() - prodNum.intValue();
                workMonthDetailVOList.stream()
                        .filter(d -> tPoWorkshopPitDayPlan.getVinasseCode().equals(d.getVinasseCode()))
                        .findFirst()
                        .ifPresent(d -> d.setPotCount(d.getPotCount() - tPoWorkshopPitDayPlan.getBalancePotCount() + balanceNum));
                tPoWorkshopPitDayPlan.setBalancePotCount(balanceNum + (tPoWorkshopPitDayPlan.getOrderRemainderCount() == null?0:tPoWorkshopPitDayPlan.getOrderRemainderCount()));
            }
        }
        //对最终数据进行过滤
        List<WorkMonthDetailVO> newDetailVOList = workMonthDetailVOList.stream().filter(d -> d.getPotCount() != null && d.getPotCount() > 0).collect(Collectors.toList());
        List<TPoWorkshopPitDayPlan> newDayPlanList = tPoWorkshopPitDayPlans.stream().filter(p -> p.getBalancePotCount() > 0 || p.getOrderRemainderCount() != null).collect(Collectors.toList());
        workMonthVO.setWorkMonthDetailVOList(newDetailVOList);
        workMonthVO.setContinueDayPlans(newDayPlanList);
        //判断当天的日计划，确认接下来应该进行什么排产
        if (CollectionUtils.isEmpty(newDayPlanList)) {
            //如果今天的计划能够全部做完，则取最后一个计划来判断
            TPoWorkshopPitDayPlan tPoWorkshopPitDayPlan = tPoWorkshopPitDayPlans.stream().max(Comparator.comparingInt(TPoWorkshopPitDayPlan::getSortNo)).orElse(null);
            if (tPoWorkshopPitDayPlan == null) {
                workMonthVO.setIsEmptyProd(true);
            } else {
                workMonthVO.setIsEmptyProd(!"CJ".equals(tPoWorkshopPitDayPlan.getVinasseCode()));
            }
        } else {
            workMonthVO.setIsEmptyProd(!"CJ".equals(newDayPlanList.get(0).getVinasseCode()));
        }
        workMonthVO.setPlanDate(planDate);
        workMonthVO.setMonthBeginDate(monthBeginDate);
        workMonthVO.setMonthEndDate(monthEndDate);
        workMonthVO.setPlanEmptyNum(planEmptyNum);
        return workMonthVO;
    }

    /**
     * 多糟源排日计划，用单甑时长排产
     * @param prodTimeList
     * @param scheduleList
     * @param increaseAmplitude
     * @param planDate
     * @return
     */
    private List<WorkshopPitDayPlanDTO> arrangeByProdTime(List<ProdConfigItem> prodTimeList, List<ProductionSchedule> scheduleList, BigDecimal increaseAmplitude, String planDate, Integer allMinute) {
        List<WorkshopPitDayPlanDTO> addList = new ArrayList<>();
        Map<String, Integer> vinasseProdTimeMap = new HashMap<>();
        //窖池订单使用的总分钟数
        int totalMinute = 0;
        for (ProductionSchedule productionSchedule : scheduleList) {
            //单甑时长
            Integer vinasseProdTime = vinasseProdTimeMap.get(productionSchedule.getVinasseName());
            if (vinasseProdTime == null) {
                vinasseProdTime = Integer.valueOf(prodTimeList.stream().filter(p -> p.getItemCode().contains(productionSchedule.getVinasseName())).map(ProdConfigItem::getItemValue).distinct().findFirst().orElse("20"));
                vinasseProdTimeMap.put(productionSchedule.getVinasseName(), vinasseProdTime);
            }
            //按订单进行扣除
            WorkshopPitDayPlanDTO workshopPitDayPlanDTO = new WorkshopPitDayPlanDTO();
            workshopPitDayPlanDTO.setOrderId(productionSchedule.getId());
            workshopPitDayPlanDTO.setPitId(productionSchedule.getPitId());
            workshopPitDayPlanDTO.setWorkshopCenterId(productionSchedule.getCenterId());
            workshopPitDayPlanDTO.setWorkshopId(productionSchedule.getLocationId());
            workshopPitDayPlanDTO.setCategoryId(productionSchedule.getCategoryId());
            workshopPitDayPlanDTO.setPlanDate(DateUtil.parse(planDate, "yyyy-MM-dd"));
            workshopPitDayPlanDTO.setIsOpened(true);
            //根据涨幅计算出窖甑口数量
            BigDecimal outPitNum = new BigDecimal(productionSchedule.getInPitNum()).multiply(increaseAmplitude).setScale(0, RoundingMode.UP);
            int thisProdTime = outPitNum.multiply(new BigDecimal(vinasseProdTime)).intValue();
            if (totalMinute + thisProdTime < allMinute) {
                //没有超过当天的生产时长
                totalMinute += thisProdTime;
                workshopPitDayPlanDTO.setCalcOutPotCount(outPitNum.intValue());
                workshopPitDayPlanDTO.setBalancePotCount(0);
                workshopPitDayPlanDTO.setPlanOutPotCount(outPitNum.intValue());
                addList.add(workshopPitDayPlanDTO);
            } else {
                //当天剩余时长已经不足以生产完该订单,计算还能生产的订单
                Integer prodDay = new BigDecimal(allMinute - totalMinute).divide(new BigDecimal(vinasseProdTime), 0, RoundingMode.FLOOR).intValue();
                workshopPitDayPlanDTO.setCalcOutPotCount(prodDay);
                workshopPitDayPlanDTO.setBalancePotCount(0);
                workshopPitDayPlanDTO.setPlanOutPotCount(prodDay);
                //设置剩余排产甑口，下次排产
                workshopPitDayPlanDTO.setOrderRemainderCount(outPitNum.intValue() - prodDay);
                addList.add(workshopPitDayPlanDTO);
                break;
            }
        }
        return addList;
    }

    /**
     * 单糟源排日计划，用每日产能排产
     *
     * @param scheduleList
     * @param increaseAmplitude
     * @param planDate
     * @return
     */
    private List<WorkshopPitDayPlanDTO> arrangeByDayCapacity(Integer dayCapacity, List<ProductionSchedule> scheduleList, BigDecimal increaseAmplitude, String planDate) {
        List<WorkshopPitDayPlanDTO> addList = new ArrayList<>();

        //产出总数 根据涨幅计算
        Map<Integer, BigDecimal> orderOutPutMap = new HashMap<>();
        //计算产能
        scheduleList.forEach(s -> orderOutPutMap.put(s.getId(), new BigDecimal(s.getInPitNum()).multiply(increaseAmplitude).setScale(0, RoundingMode.UP)));
//        BigDecimal outPitNumAll = orderOutPutMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
//        if (dayCapacity > outPitNumAll.intValue()) {
//            //排产不能直接覆盖每日产能
//        }
        //用涨幅进行计算出甑口
        for (ProductionSchedule productionSchedule : scheduleList) {
            BigDecimal outPitNum = orderOutPutMap.get(productionSchedule.getId());
            //按订单进行扣除
            WorkshopPitDayPlanDTO workshopPitDayPlanDTO = new WorkshopPitDayPlanDTO();
            workshopPitDayPlanDTO.setOrderId(productionSchedule.getId());
            workshopPitDayPlanDTO.setPitId(productionSchedule.getPitId());
            workshopPitDayPlanDTO.setWorkshopCenterId(productionSchedule.getCenterId());
            workshopPitDayPlanDTO.setWorkshopId(productionSchedule.getLocationId());
            workshopPitDayPlanDTO.setCategoryId(productionSchedule.getCategoryId());
            workshopPitDayPlanDTO.setPlanDate(DateUtil.parse(planDate, "yyyy-MM-dd"));
            workshopPitDayPlanDTO.setIsOpened(true);
            //订单数量超出排产
            if (dayCapacity - outPitNum.intValue() < 0) {
                workshopPitDayPlanDTO.setCalcOutPotCount(dayCapacity);
                workshopPitDayPlanDTO.setBalancePotCount(0);
                workshopPitDayPlanDTO.setPlanOutPotCount(dayCapacity);
                //设置剩余排产甑口，下次排产
                workshopPitDayPlanDTO.setOrderRemainderCount(outPitNum.intValue() - dayCapacity);
                addList.add(workshopPitDayPlanDTO);
                break;
            } else {
                workshopPitDayPlanDTO.setCalcOutPotCount(outPitNum.intValue());
                workshopPitDayPlanDTO.setBalancePotCount(0);
                workshopPitDayPlanDTO.setPlanOutPotCount(outPitNum.intValue());
                addList.add(workshopPitDayPlanDTO);
            }
            dayCapacity = dayCapacity - outPitNum.intValue();
        }
        int addAll = addList.stream().mapToInt(WorkshopPitDayPlanDTO::getCalcOutPotCount).sum();
        //设置最终的总甑口数
        addList.forEach(p -> p.setPotCount(addAll));
        return addList;
    }

}
