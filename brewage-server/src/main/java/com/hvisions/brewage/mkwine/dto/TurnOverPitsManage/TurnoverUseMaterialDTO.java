package com.hvisions.brewage.mkwine.dto.TurnOverPitsManage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "翻窖使用物料dto")
public class TurnoverUseMaterialDTO {

    @ApiModelProperty("类型 大曲 回酒")
    private String type;

    @ApiModelProperty("使用的物料编码")
    private String materialCode;

    @ApiModelProperty("使用的量")
    private BigDecimal useCount;

    @ApiModelProperty("车间id")
    private Integer centerId;

    @ApiModelProperty("车间id")
    private Integer locationId;

    @ApiModelProperty("跨号 -- 大曲使用需要传")
    private String lineName;

    @ApiModelProperty("以前的已经传过来的数据，没传则是空")
    private List<UseMaterialBatchDTO> batchDTOList;

    @ApiModelProperty("是否有库存--查询逻辑使用")
    private Boolean inStock = true;

    @ApiModelProperty("上一次使用的批次--查询逻辑使用")
    private String lastBatch;

    @ApiModelProperty("数据id")
    private Integer dataId;
}
