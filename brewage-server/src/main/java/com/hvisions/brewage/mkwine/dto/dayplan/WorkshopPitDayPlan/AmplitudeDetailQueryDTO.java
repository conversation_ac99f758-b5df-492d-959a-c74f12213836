package com.hvisions.brewage.mkwine.dto.dayplan.WorkshopPitDayPlan;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "近期涨幅详情分页条件dto")
public class AmplitudeDetailQueryDTO extends PageInfo {

    @ApiModelProperty(value = "近期涨幅id")
    private Integer increaseAmplitudeId;

    @ApiModelProperty(value = "生成数据的查询的起始时间")
    private Date initiationTime;

    @ApiModelProperty(value = "车间id")
    private Integer locationId;

}
