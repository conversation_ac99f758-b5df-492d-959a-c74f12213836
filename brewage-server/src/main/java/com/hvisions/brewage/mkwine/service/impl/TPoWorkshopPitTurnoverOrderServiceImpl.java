package com.hvisions.brewage.mkwine.service.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hvisions.auth.dto.user.UserInfoDto;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.bw.dao.TaskDetailMapper;
import com.hvisions.brewage.bw.dao.TaskMapper;
import com.hvisions.brewage.bw.entity.Task;
import com.hvisions.brewage.bw.entity.TaskDetail;
import com.hvisions.brewage.common.constant.MqQueueConstant;
import com.hvisions.brewage.common.entity.MessageConsume;
import com.hvisions.brewage.common.utils.AmqpTemplateUtil;
import com.hvisions.brewage.common.utils.RedissonLockUtil;
import com.hvisions.brewage.common.utils.SnowflakeIdWorker;
import com.hvisions.brewage.dto.tpo.WorkshopPitTurnoverOrderQueryDTO;
import com.hvisions.brewage.enums.OtherEnum;
import com.hvisions.brewage.feign.message.MessageConsumeClient;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.ReportHandler.TurnoverOrderHandler;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderPotTaskMapper;
import com.hvisions.brewage.mkwine.dao.ProductionProcess.WorkshopPitOrderSapMapper;
import com.hvisions.brewage.mkwine.dao.TurnOverPitManage.TPoWorkshopPitTurnoverOrderDetailMapper;
import com.hvisions.brewage.mkwine.dao.TurnOverPitManage.TPoWorkshopPitTurnoverOrderPlanMapper;
import com.hvisions.brewage.mkwine.dao.WorkshopPitMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.TechnologicalRequirementsMapper;
import com.hvisions.brewage.mkwine.dao.productiondisposition.WorkshopFullPitMapper;
import com.hvisions.brewage.mkwine.dto.QueryUserByRoleDTO;
import com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.*;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrder;
import com.hvisions.brewage.mkwine.entity.ProductionProcess.TPoWorkshopPitOrderSap;
import com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrderDetail;
import com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrderPlan;
import com.hvisions.brewage.mkwine.entity.productiondisposition.TPoWorkshopFullPit;
import com.hvisions.brewage.mkwine.enums.ExecutionStatus;
import com.hvisions.brewage.mkwine.enums.SapCancelType;
import com.hvisions.brewage.mkwine.service.*;
import com.hvisions.brewage.mkwine.vo.AjaxResult;
import com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.ChoosePitOrderVO;
import com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.PitDataInfo;
import com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.TurnOverPitBoardVO;
import com.hvisions.brewage.mkwine.vo.UserVO;
import com.hvisions.brewage.mkwine.vo.productiondisposition.TechnologicalRequirements.TechnologicalRequirementsVO;
import com.hvisions.brewage.mq.dto.BaseOrderMessageSapDTO;
import com.hvisions.brewage.service.tpo.TaskOrderBatchService;
import com.hvisions.brewage.service.tpo.TaskProcessRecordsService;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.IdHelper;
import com.hvisions.brewage.utils.StringUtil;
import com.hvisions.brewage.vo.tpo.FJMaterialCodeVO;
import com.hvisions.brewage.vo.tpo.TPoWorkshopPitTurnoverOrderVO;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.schedule.client.CrewClient;
import com.hvisions.schedule.dto.CrewWithMemberDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.hvisions.brewage.mkwine.dao.TurnOverPitManage.TPoWorkshopPitTurnoverOrderMapper;
import com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrder;
import org.springframework.transaction.annotation.Transactional;

/**
 * 翻窖任务service
 *
 * @BelongsProject: brewage
 * @BelongsPackage: com.hvisions.brewage.mkwine.service.impl
 * <AUTHOR>
 * @Date 2022-05-30  11:24
 * @Version: 1.0
 */

@Service
@Slf4j
public class TPoWorkshopPitTurnoverOrderServiceImpl implements TPoWorkshopPitTurnoverOrderService {

    final Pattern pattern = Pattern.compile("[^0-9]");

    @Resource
    private TPoWorkshopPitTurnoverOrderMapper tPoWorkshopPitTurnoverOrderMapper;

    @Resource
    private WorkshopPitOrderSapMapper workshopPitOrderSapMapper;

    @Autowired
    private TPoWorkshopPitTurnoverOrderDetailMapper detailMapper;

    @Autowired
    SendMessageService messageService;

    @Resource
    TechnologicalRequirementsMapper technologicalRequirementsMapper;

    @Resource
    WorkshopPitOrderPotTaskMapper workshopPitOrderPotTaskMapper;

    @Resource
    WorkshopPitMapper workshopPitMapper;

    @Resource
    IdHelper idHelper;

    @Resource
    CrewClient crewClient;

    @Resource
    TPoWorkshopPitTurnoverOrderPlanMapper turnoverOrderPlanMapper;

    @Resource
    WorkshopPitOrderMapper workshopPitOrderMapper;

    @Resource
    CommonService commonService;

    @Autowired
    TPoWorkshopPitTurnoverOrderDetailService detailService;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    TaskDetailMapper taskDetailMapper;

    @Resource
    private TaskOrderBatchService taskOrderBatchService;

    @Resource
    TaskMapper taskMapper;

    @Resource
    private WorkshopPitOrderService workshopPitOrderService;

    @Resource
    private MessageConsumeClient messageConsumeClient;

    @Resource
    WorkshopFullPitMapper workshopFullPitMapper;

    @Resource
    private TaskProcessRecordsService taskProcessRecordsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult deleteByPrimaryKey(Integer id, String code) {
//        List<TPoWorkshopPitTurnoverOrderDetail> tPoWorkshopPitTurnoverOrderDetails = detailMapper.seletAllData(code);
//        if (tPoWorkshopPitTurnoverOrderDetails.size() > 0) {
//            throw new BaseKnownException(10000, "翻窖任务：" + code + " 存在翻窖数据，不可删除");
//        }

        //删除PDA个人任务执行进度
        taskProcessRecordsService.deleteTask(code,ExecutionStatus.CELLAR_TURN.getStatus());

        // 更新主表
        int row = tPoWorkshopPitTurnoverOrderMapper.deleteByPrimaryKey(id);
        if (row != 1) {
            return AjaxResult.error();
        }
        detailMapper.batchDelete(code);

        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateByPrimaryKeySelective(TPoWorkshopPitTurnoverOrder record) {

        int row = tPoWorkshopPitTurnoverOrderMapper.updateByPrimaryKeySelective(record);
        TPoWorkshopPitTurnoverOrder turnoverOrder = tPoWorkshopPitTurnoverOrderMapper.selectById(record.getId());

        // 下发的时候要发送信息
        if (row > 0 && record.getOrderStatus() == 1) {

            // 下发的时候把窖池订单的翻窖状态改为翻窖中
            List<TPoWorkshopPitTurnoverOrderPlan> orderPlans = turnoverOrderPlanMapper.selectAllByTurnoverOrderId(record.getId());
            List<TPoWorkshopPitOrder> list = new ArrayList<>();

            orderPlans.forEach(p -> {
                if (p.getPitOrderId() != null) {
                    TPoWorkshopPitOrder order = new TPoWorkshopPitOrder();
                    order.setId(p.getPitOrderId());
                    order.setTurnoverStatus(6);
                    log.info("下发时修改窖池订单翻窖次数，窖池订单id：{},次数：{}", p.getPitOrderId(), turnoverOrder.getTurnoverTimes());
                    order.setTurnoverCount(turnoverOrder.getTurnoverTimes());
                    list.add(order);

                    //新增窖池订单执行状态
                    workshopPitOrderService.putExecutionStatus(p.getPitOrderCode(), ExecutionStatus.CELLAR_TURN.getStatus(), 1);

                    //创建PDA个人任务执行进度
                    taskProcessRecordsService.addTask(turnoverOrder.getTurnoverOrderCode(),p.getPitCode(),ExecutionStatus.CELLAR_TURN.getStatus(),turnoverOrder.getCenterId(),turnoverOrder.getLocationId());
                }
            });

            if (list.size() > 0) {
                list.forEach(v -> {
                    workshopPitOrderMapper.updateById(v);
                });
                // workshopPitOrderMapper.updateBatchSelective(list);
            }

            ResultVO<CrewWithMemberDTO> crewByIdData = crewClient.getCrewById(record.getCrewId());
            List<Integer> user = crewByIdData.getData().getUserInfoDtos().stream().map(UserInfoDto::getId).collect(Collectors.toList());
            messageService.sendMessageByUser(record.getTurnoverOrderCode(), "翻窖任务已下发，请及时完成", user);
        }
        return row > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public int updateBatch(List<TPoWorkshopPitTurnoverOrder> list) {
        return tPoWorkshopPitTurnoverOrderMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<TPoWorkshopPitTurnoverOrder> list) {
        return tPoWorkshopPitTurnoverOrderMapper.batchInsert(list);
    }

    @Override
    public Page<TPoWorkshopPitTurnoverOrder> getOrderByPage(QueryPitTurnOverDTO dto) {
        Page<TPoWorkshopPitTurnoverOrder> page = new Page<>(dto.getPage(), dto.getPageSize());
        List<TPoWorkshopPitTurnoverOrder> turnoverOrderList = tPoWorkshopPitTurnoverOrderMapper.getOrderByPage(page, dto);
        page.setRecords(turnoverOrderList);
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertOrderAndDetail(SavePitTurnOverOrderDTO dto) {
        TPoWorkshopPitTurnoverOrder order = dto.getOrder();
        String code = "";
        try {
            code = idHelper.getExpireId("FJ");
        } catch (Exception e) {
            return AjaxResult.error("获取翻窖号失败，请稍后重试");
        }
        order.setTurnoverOrderCode(code);

        // 插入主表数据
        tPoWorkshopPitTurnoverOrderMapper.insertSelective(order);

        // 插入子表数据
        int index = 1;
        for (TPoWorkshopPitTurnoverOrderDetail detail : dto.getItemDetail()) {
            detail.setTurnoverOrderCode(code);
            detail.setRowIndex(index++);
        }

        detailMapper.batchInsert(dto.getItemDetail());


        return AjaxResult.success();
    }

    //@Override
    //@Transactional(rollbackFor = Exception.class)
    //public AjaxResult updateOrderAndDetail(SavePitTurnOverOrderDTO dto) {
    //
    //    TPoWorkshopPitTurnoverOrder order = dto.getOrder();
    //    List<TPoWorkshopPitTurnoverOrderDetail> detailItems = dto.getItemDetail();
    //
    //    // 如果是完成状态，直接更新数据既可
    //    if (order.getOrderStatus() == 2) {
    //        // 更新主表
    //        tPoWorkshopPitTurnoverOrderMapper.updateByPrimaryKeySelective(order);
    //        // 更新子表
    //        detailMapper.updateBatchSelective(detailItems);
    //        tPoWorkshopPitTurnoverOrderMapper.updatePitOrder(detailItems);
    //        return AjaxResult.success();
    //    }
    //
    //    tPoWorkshopPitTurnoverOrderMapper.updateByPrimaryKeySelective(order);
    //
    //    // 获取子表数据
    //    List<TPoWorkshopPitTurnoverOrderDetail> details = detailMapper.seletAllData(order.getTurnoverOrderCode());
    //    // 获取id
    //    List<Integer> idLists = details.stream().map(TPoWorkshopPitTurnoverOrderDetail::getId).collect(Collectors.toList());
    //
    //    // 新增
    //    List<TPoWorkshopPitTurnoverOrderDetail> newItems = new ArrayList<>();
    //    // 更新
    //    List<TPoWorkshopPitTurnoverOrderDetail> updateItems = new ArrayList<>();
    //
    //    List<Integer> itemIdList = new ArrayList<>();
    //
    //    for (TPoWorkshopPitTurnoverOrderDetail detail : detailItems) {
    //
    //        // 新增
    //        if (detail.getId() == null) {
    //            detail.setTurnoverOrderCode(order.getTurnoverOrderCode());
    //            newItems.add(detail);
    //            continue;
    //        }
    //
    //        itemIdList.add(detail.getId());
    //
    //        // 更新
    //        if (detail.getId() != null && idLists.contains(detail.getId())) {
    //            updateItems.add(detail);
    //        }
    //
    //    }
    //
    //    // 删除
    //    List<Integer> removeItems = idLists.stream().filter(p -> !itemIdList.contains(p)).collect(Collectors.toList());
    //
    //    if (newItems.size() != 0) {
    //        detailMapper.batchInsert(newItems);
    //    }
    //
    //    // Delete和update其实是一样的，把它分开更好区别
    //
    //    if (removeItems.size() != 0) {
    //        detailMapper.batchDeleteInId(removeItems);
    //    }
    //
    //    if (updateItems.size() != 0) {
    //        detailMapper.updateBatchSelective(updateItems);
    //        tPoWorkshopPitTurnoverOrderMapper.updatePitOrder(updateItems);
    //    }
    //
    //    return AjaxResult.success();
    //}

    @Override
    public List<ChoosePitOrderVO> selectPitOrderData(QueryPitOrderDTO dto) {
        List<ChoosePitOrderVO> choosePitOrderVOS = tPoWorkshopPitTurnoverOrderMapper.selectPitOrderData(dto, false);

        if (CollectionUtils.isEmpty(choosePitOrderVOS)) {
            return new ArrayList<>();
        }

        // ========================== 2022-09-07 新需求 需要添加导入的甑口 ==========================

        List<ChoosePitOrderVO> otherInputNumExc = tPoWorkshopPitTurnoverOrderMapper.selectExcInputNum(choosePitOrderVOS.stream().map(ChoosePitOrderVO::getId).collect(Collectors.toList()));
        for (ChoosePitOrderVO orderVO : choosePitOrderVOS) {
            otherInputNumExc.stream().filter(p -> p.getId().equals(orderVO.getId())).findFirst().ifPresent(p -> {
                orderVO.setInPitNum(getPotNum(orderVO.getInPitNum(), p.getInPitNum(), true));
            });
        }

        return choosePitOrderVOS.stream().filter(p -> p.getInPitNum() != null).collect(Collectors.toList());
    }


    /**
     * 获取翻窖窖池看板数据
     *
     * @param dto
     * @return
     */
    @Override
    public List<TurnOverPitBoardVO> getTurnOverPitBoardData(QueryPitOrderDTO dto) {

        List<TurnOverPitBoardVO> list = new ArrayList<>();

        //根据中心Id和locationId获取所有窖池
        List<ChoosePitOrderVO> pitData = workshopPitMapper.turnOverGetPitData(dto.getCentreId(), dto.getLocationId());

        if (CollectionUtils.isEmpty(pitData)) {
            return list;
        }

        //根据传来的数据筛选获取所有订单
        List<ChoosePitOrderVO> pitOrderData = tPoWorkshopPitTurnoverOrderMapper.selectPitOrderData(dto, true);

        // 导入的甑口
        List<ChoosePitOrderVO> otherInputNumExc = tPoWorkshopPitTurnoverOrderMapper.selectExcInputNum(pitData.stream().map(ChoosePitOrderVO::getId).collect(Collectors.toList()));

        HashMap<Integer, List<ChoosePitOrderVO>> hashMap = new HashMap<>();
        for (ChoosePitOrderVO orderVO : pitOrderData) {

            otherInputNumExc.stream().filter(p -> p.getId().equals(orderVO.getId())).findFirst().ifPresent(p -> {
                orderVO.setInPitNum(getPotNum(orderVO.getInPitNum(), p.getInPitNum(), true));
            });


            if (hashMap.containsKey(orderVO.getPitId())) {
                hashMap.get(orderVO.getPitId()).add(orderVO);
                continue;
            }

            hashMap.put(orderVO.getPitId(), Lists.newArrayList(orderVO));
        }

        for (ChoosePitOrderVO orderVO : pitData) {
            addAcrossData(list, orderVO, orderVO.getArea(), hashMap);
        }

        // 重新排序赋值
        List<TurnOverPitBoardVO> collect = list.stream().sorted(Comparator.comparing(TurnOverPitBoardVO::getAcross)).collect(Collectors.toList());
        AtomicInteger index = new AtomicInteger(1);
        collect.forEach(p -> {
            p.setId(index.getAndIncrement());
            if (p.getItemData().size() > 0) {
                for (PitDataInfo itemDatum : p.getItemData()) {
                    for (ChoosePitOrderVO datum : itemDatum.getItemData()) {
                        if (StringUtil.isNotEmpty(datum.getPitStatus()) && datum.getPitStatus() == 6) {
                            datum.setPitStatus(7);
                        }
                    }
                    if (StringUtil.isNotEmpty(itemDatum.getPitStatus()) && itemDatum.getPitStatus() == 6) {
                        // 翻窖中就是翻窖完成
                        itemDatum.setPitStatus(7);
                    }
                }
            }
        });

        return collect;
    }

    /**
     * 设置数据是否可视化
     */
    private void setDataVisibility(QueryPitOrderDTO dto, ChoosePitOrderVO orderVO) {

        boolean visible = true;

        if (dto.getArea() != null) {
            visible = orderVO.getArea().contains(dto.getArea());
        }

        if (dto.getPitCode() != null) {
            visible = visible && orderVO.getPitCode().contains(dto.getPitCode());
        }

        if (dto.getFermentationDay() != null) {
            if (orderVO.getFermentationDay() != null) {
                visible = visible && (dto.getFermentationDay() <= orderVO.getFermentationDay());
            } else {
                visible = false;
            }
        }

        if (dto.getVinasseId() != null) {
            visible = visible && dto.getVinasseId().equals(orderVO.getVinasseId());
        }

        orderVO.setIsVisibility(visible);
    }

    /**
     * 添加跨
     *
     * @param list
     */
    private void addAcrossData(List<TurnOverPitBoardVO> list, ChoosePitOrderVO choosePitOrder, String area, HashMap<Integer, List<ChoosePitOrderVO>> hashMap) {

        Matcher matcher = pattern.matcher(StringUtils.isBlank(area) ? "未知跨" : area);
        String acrossNum = matcher.replaceAll("").trim();

        if (StringUtils.isBlank(acrossNum)) {
            acrossNum = "9999";
            area = "未知跨";
        }

        String finalArea = area;
        TurnOverPitBoardVO boardVO = list.stream().filter(p -> p.getName() != null && p.getName().equals(finalArea)).findFirst().orElse(null);

        if (boardVO == null) {
            boardVO = new TurnOverPitBoardVO();
            boardVO.setAcross(Integer.parseInt(acrossNum));
            boardVO.setName(area);
            boardVO.setItemData(new ArrayList<>());

            list.add(boardVO);
        }

        if (boardVO.getMaxRow() < choosePitOrder.getTheRow()) {
            boardVO.setMaxRow(choosePitOrder.getTheRow());
        }

        if (boardVO.getMaxColumn() < choosePitOrder.getTheColumn()) {
            boardVO.setMaxColumn(choosePitOrder.getTheColumn());
        }

        PitDataInfo pitDataInfo = new PitDataInfo();
        pitDataInfo.setPitId(choosePitOrder.getPitId());
        pitDataInfo.setPitCode(choosePitOrder.getPitCode());
        pitDataInfo.setTheRow(choosePitOrder.getTheRow());
        pitDataInfo.setArea(choosePitOrder.getArea());
        pitDataInfo.setTheColumn(choosePitOrder.getTheColumn());
        pitDataInfo.setItemData(new ArrayList<>());

        boardVO.getItemData().add(pitDataInfo);

        for (PitDataInfo info : boardVO.getItemData()) {
            if (!info.getPitId().equals(choosePitOrder.getPitId())) {
                continue;
            }
            info.setItemData(hashMap.getOrDefault(choosePitOrder.getPitId(), new ArrayList<>()));
        }

    }

    @Override
    public AjaxResult auditData(TPoWorkshopPitTurnoverOrder turnoverOrder) {

        turnoverOrder.setOrderStatus(3);

        // 判空
        if (StringUtils.isBlank(turnoverOrder.getTurnoverOrderCode())) {
            return AjaxResult.error("翻窖任务号为空");
        }

        if (turnoverOrder.getBackAlcoholicQuantity() == null) {
            return AjaxResult.error("回酒量为空");
        }

        if (turnoverOrder.getQuQuantity() == null) {
            return AjaxResult.error("翻沙曲为空");
        }


        // 翻窖明细
        List<TPoWorkshopPitTurnoverOrderDetail> details = detailMapper.seletAllData(turnoverOrder.getTurnoverOrderCode());
        double sumData = details.stream().mapToDouble(p -> (p.getInPotNum() == null ? 0 : p.getInPotNum())).sum();

        int inPotNum;
        double backAlcoholicQuantity;
        double quQuantity;

        for (TPoWorkshopPitTurnoverOrderDetail detail : details) {
            backAlcoholicQuantity = turnoverOrder.getBackAlcoholicQuantity();
            quQuantity = turnoverOrder.getQuQuantity();
            inPotNum = (detail.getInPotNum() == null ? 0 : detail.getInPotNum());

            // 设置翻沙曲
            BigDecimal quQuantityAverage = BigDecimal.valueOf((inPotNum / sumData) * quQuantity).setScale(2, RoundingMode.HALF_UP);
            detail.setQuQuantity(quQuantityAverage.doubleValue());

            // 设置回酒
            BigDecimal backAlcoholicQuantityAverage = BigDecimal.valueOf((inPotNum / sumData) * backAlcoholicQuantity).setScale(2, RoundingMode.HALF_UP);
            detail.setBackAlcoholicQuantity(backAlcoholicQuantityAverage.doubleValue());
        }

        tPoWorkshopPitTurnoverOrderMapper.updateByPrimaryKeySelective(turnoverOrder);
        detailMapper.updateBatchSelective(details);

        return AjaxResult.success();
    }

    // =============================== 翻窖任务（新）==================================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertTurnOverTask(SaveTurnOverTaskDTO dto) {
        log.info("新增翻窖任务，传入参数------》{}", JSONObject.toJSONString(dto));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增翻窖任务，获取到当前登录用户，用户id：{}", userId);

        TPoWorkshopPitTurnoverOrder order = dto.getOrder();
        String code = "";
        try {
            code = idHelper.getExpireId("FJ");
        } catch (Exception e) {
            return AjaxResult.error("获取翻窖号失败，请稍后重试");
        }
        order.setTurnoverOrderCode(code);
        order.setCreatorId(userId);
        order.setCreateTime(new Date());

        // 插入主表数据
        tPoWorkshopPitTurnoverOrderMapper.insert(order);

        // 插入子表数据
        dto.getItemDetail().forEach(p -> {
            if (p.getIntPotNum() == null) {
                p.setIntPotNum(0);
            }
            p.setTurnoverOrderId(order.getId());
        });

        turnoverOrderPlanMapper.batchInsert(dto.getItemDetail());

        return AjaxResult.success();
    }

    @Override
    public List<TPoWorkshopPitTurnoverOrderPlan> getTurnoverPlanPitData(Integer turnoverOrderId) {
        List<TPoWorkshopPitTurnoverOrderPlan> list = turnoverOrderPlanMapper.selectAllByTurnoverOrderId(turnoverOrderId);
        if (list == null) {
            return new ArrayList<>();
        }

        list.forEach(p -> {
            if (p.getTurnoverPotNum() == null) {
                p.setTurnoverPotNum(p.getIntPotNum());
            }
        });

        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateTurnOverTask(SaveTurnOverTaskDTO dto) {
        log.info("新增翻窖任务，传入参数------》{}", JSONObject.toJSONString(dto));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增翻窖任务，获取到当前登录用户，用户id：{}", userId);

        TPoWorkshopPitTurnoverOrder order = dto.getOrder();
        List<TPoWorkshopPitTurnoverOrderPlan> detailItems = dto.getItemDetail();

        order.setUpdaterId(userId);
        order.setUpdateTime(new Date());
        tPoWorkshopPitTurnoverOrderMapper.updateById(order);

        List<TPoWorkshopPitTurnoverOrderPlan> pitOrderPlans = turnoverOrderPlanMapper.selectAllByTurnoverOrderId(order.getId());

        if (pitOrderPlans == null) {
            pitOrderPlans = new ArrayList<>();
        }

        // 新增
        List<TPoWorkshopPitTurnoverOrderPlan> newItems = new ArrayList<>();
        // 更新
        List<TPoWorkshopPitTurnoverOrderPlan> updateItems = new ArrayList<>();


        // 获取新增的数据
        for (TPoWorkshopPitTurnoverOrderPlan pitOrderPlan : detailItems) {
            pitOrderPlan.setTurnoverOrderId(order.getId());
            // 因为前端难获取id，所以通过窖池订单号来查找
            TPoWorkshopPitTurnoverOrderPlan orderPlan = pitOrderPlans.stream().filter(p -> p.getPitOrderCode().equals(pitOrderPlan.getPitOrderCode())).findFirst().orElse(null);
            if (orderPlan == null) {
                newItems.add(pitOrderPlan);
            } else {
                pitOrderPlan.setId(orderPlan.getId());
                pitOrderPlan.setTurnoverOrderId(orderPlan.getTurnoverOrderId());
                updateItems.add(pitOrderPlan);
            }
        }

        // 获取删除的数据
        for (TPoWorkshopPitTurnoverOrderPlan pitOrderPlan : pitOrderPlans) {
            TPoWorkshopPitTurnoverOrderPlan orderPlan = detailItems.stream().filter(p -> p.getPitOrderCode().equals(pitOrderPlan.getPitOrderCode())).findFirst().orElse(null);
            if (orderPlan == null) {
                pitOrderPlan.setIsDeleted(true);
                updateItems.add(pitOrderPlan);
            }
        }

        if (updateItems.size() > 0) {
            turnoverOrderPlanMapper.updateBatchSelective(updateItems);
        }

        if (newItems.size() > 0) {
            turnoverOrderPlanMapper.batchInsert(newItems);
        }


        return AjaxResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult insertTurnOverDetail(SaveTurnoverDetailDTO dto) {

        TPoWorkshopPitTurnoverOrderDetail detail = dto.getDetail();
        List<TPoWorkshopPitTurnoverOrderPlan> pitOrderPlans = dto.getPitOrderPlans();

        if (pitOrderPlans == null) {
            return AjaxResult.error("翻窖计划窖池数据为空");
        }

        List<TPoWorkshopPitTurnoverOrderPlan> collect = pitOrderPlans.stream().filter(p -> p.getId().equals(detail.getFirstPitDataId()) || p.getId().equals(detail.getSecondPitDataId())).collect(Collectors.toList());
        if (collect.size() == 0) {
            return AjaxResult.error("翻窖计划窖池数据异常");
        }

        List<TPoWorkshopPitTurnoverOrderPlan> list = modifyPlanData(collect, detail);

        // 自己翻自己的要做特殊处理
        if (detail.getFirstPitDataId().equals(detail.getSecondPitDataId())) {
            detail.setInPotNum(detail.getOutPotFinishNum());
            detail.setInPotFinishNum(detail.getPotsNum());
        }

        if (list.size() > 0) {
            // 更新翻窖任务的计划窖池数据
            turnoverOrderPlanMapper.updateBatchSelective(list);
        }

        return detailMapper.insertSelective(detail) > 0 ? AjaxResult.success() : AjaxResult.error();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult updateTurnOverDetail(SaveTurnoverDetailDTO dto) {

        if (dto.getTurnoverTaskId() == null) {
            return AjaxResult.error("翻窖任务ID为空");
        }

        TPoWorkshopPitTurnoverOrderDetail detail = dto.getDetail();

        List<TPoWorkshopPitTurnoverOrderPlan> list = turnoverOrderPlanMapper.selectAllByTurnoverOrderId(dto.getTurnoverTaskId());
        List<TPoWorkshopPitTurnoverOrderDetail> details = detailMapper.seletAllData(detail.getTurnoverOrderCode());

        if (details == null || details.size() == 0) {
            return AjaxResult.error("翻窖任务明细为空");
        }

        Integer index = null;

        for (int i = 0; i < details.size(); i++) {
            if (details.get(i).getId().equals(detail.getId())) {

                details.get(i).setFirstPitDataId(detail.getFirstPitDataId());
                details.get(i).setSecondPitDataId(detail.getSecondPitDataId());

                details.get(i).setOutPotNum(detail.getOutPotNum());
                details.get(i).setIsDelete(detail.getIsDelete());
                details.get(i).setTurnoverTime(detail.getTurnoverTime());


                index = i;
                break;
            }
        }

        if (index == null) {
            return AjaxResult.error("未找到该条数据的信息");
        }

        resetTurnoverData(list, details);

        // 更新翻窖任务的计划窖池数据
        List<TPoWorkshopPitTurnoverOrderPlan> collect = list.stream().filter(p -> p.getIsUsed() != null && p.getIsUsed()).collect(Collectors.toList());
        if (collect.size() > 0) {
            turnoverOrderPlanMapper.updateBatchSelective(collect);
        }

        detailMapper.updateBatchSelective(details);


        if (detail.getIsDelete() == 1) {
            details.remove(index.intValue());
        }

        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put("data", details);

        return ajaxResult;
    }

    /**
     * 翻窖完成
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult finishTurnoverTask(SavePitTurnOverOrderDTO dto) {
        log.info("调用翻窖任务完成，传入参数------》{}", JSONObject.toJSONString(dto));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用翻窖任务完成，获取到当前登录用户，用户id：{}", userId);

        TPoWorkshopPitTurnoverOrder order = dto.getOrder();
        List<TPoWorkshopPitTurnoverOrderDetail> detailItems = dto.getItemDetail();
        // 获取翻窖计划窖池
        List<TPoWorkshopPitTurnoverOrderPlan> planList = turnoverOrderPlanMapper.selectAllByTurnoverOrderId(order.getId()).stream().filter(p -> p.getTurnoverPotNum() != null).collect(Collectors.toList());


        // 更新主表
        order.setOrderStatus(2);
        order.setUpdaterId(userId);
        order.setUpdateTime(new Date());
        tPoWorkshopPitTurnoverOrderMapper.updateByPrimaryKeySelective(order);

        if (detailItems.size() > 0) {
            // 获取总重量
            Double quQuantity = order.getQuQuantity();
            Double backAlcoholicQuantity = order.getBackAlcoholicQuantity();
            Double hsQuantity = order.getHsQuantity();
            Double wjQuantity = order.getWjQuantity();
            Double sdjQuantity = order.getSdjQuantity();
            // 按照占比分配
            double potSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getInPotFinishNum).sum();
            for (TPoWorkshopPitTurnoverOrderDetail detailItem : detailItems) {
                detailItem.setQuQuantity(Double.parseDouble(new DecimalFormat("#").format(quQuantity * (detailItem.getInPotFinishNum() / potSum))));
                detailItem.setBackAlcoholicQuantity(Double.parseDouble(new DecimalFormat("#").format(backAlcoholicQuantity * (detailItem.getInPotFinishNum() / potSum))));
                detailItem.setHsQuantity(Double.parseDouble(new DecimalFormat("#").format(hsQuantity * (detailItem.getInPotFinishNum() / potSum))));
                detailItem.setWjQuantity(Double.parseDouble(new DecimalFormat("#").format(wjQuantity * (detailItem.getInPotFinishNum() / potSum))));
                detailItem.setSdjQuantity(Double.parseDouble(new DecimalFormat("#").format(sdjQuantity * (detailItem.getInPotFinishNum() / potSum))));
            }
            // 计算差值，后修正
            //这里只是为了将上一步计算的余数，添加到第一条数据里面
            double quSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getQuQuantity).sum();
            double backSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getBackAlcoholicQuantity).sum();

            double hsSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getHsQuantity).sum();
            double wjSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getWjQuantity).sum();
            double sdjSum = detailItems.stream().mapToDouble(TPoWorkshopPitTurnoverOrderDetail::getSdjQuantity).sum();

            detailItems.get(0).setQuQuantity(detailItems.get(0).getQuQuantity() + (quQuantity - quSum));
            detailItems.get(0).setBackAlcoholicQuantity(detailItems.get(0).getBackAlcoholicQuantity() + (backAlcoholicQuantity - backSum));
            detailItems.get(0).setHsQuantity(detailItems.get(0).getHsQuantity() + (hsQuantity - hsSum));
            detailItems.get(0).setWjQuantity(detailItems.get(0).getWjQuantity() + (wjQuantity - wjSum));
            detailItems.get(0).setSdjQuantity(detailItems.get(0).getSdjQuantity() + (sdjQuantity - sdjSum));
            // 更新子表
            detailItems.forEach(v -> {
                detailMapper.updateById(v);
            });

        }

        if (planList.size() > 0) {

            boolean needChange = false;
            Integer vinasseId = null;
            //  修改连窖订单的糟源类型
            //TechnologicalRequirementsVO data = technologicalRequirementsMapper.findDataByCategoryId(dto.getOrder().getVinasseId());
            TechnologicalRequirementsVO data = new TechnologicalRequirementsVO();
            //进行了糟源转换
            if (!dto.getOrder().getUpdateVinasseId().equals(dto.getOrder().getVinasseId())) {
                //2024-9-4 改，不再采用配方工艺糟源，由页面选择糟源
                data.setNewCategoryId(dto.getOrder().getUpdateVinasseId());
                data.setNewVinasse(dto.getOrder().getUpdateVinasseName());
                needChange = true;
                vinasseId = data.getNewCategoryId();
                List<Integer> orderIdList = planList.stream().map(TPoWorkshopPitTurnoverOrderPlan::getPitOrderId).distinct().collect(Collectors.toList());

                // 获取单窖订单
                List<TPoWorkshopPitOrderSap> tPoWorkshopPitOrderSaps = workshopPitOrderSapMapper.getWorkshopPitSapDataByList(orderIdList);

                // 获取甑口任务数
                List<ChoosePitOrderVO> inPutCountByOrderCode = workshopPitOrderPotTaskMapper.getInPutCountByOrderCode(tPoWorkshopPitOrderSaps.stream().map(TPoWorkshopPitOrderSap::getOrderCode).collect(Collectors.toList()));

                // 增加甑口数
                for (ChoosePitOrderVO choosePitOrderVO : inPutCountByOrderCode) {
                    tPoWorkshopPitOrderSaps.stream().filter(p -> p.getOrderCode().equals(choosePitOrderVO.getOrderCode())).findFirst().ifPresent(p -> {
                        choosePitOrderVO.setInPitNum(getPotNum(choosePitOrderVO.getInPitNum(), p.getInPotNumExc(), true));
                    });
                }

                sendMessageToSap(detailItems, tPoWorkshopPitOrderSaps, inPutCountByOrderCode, data, planList);
            }

            // 修改连窖订单
            tPoWorkshopPitTurnoverOrderMapper.updatePitOrder(planList, needChange, vinasseId);
            long packageId = SnowflakeIdWorker.getNextId();
            //修改连窖的甑口数和糟源
            planList.forEach(v -> {
                TPoWorkshopPitOrder tPoWorkshopPitOrder = workshopPitOrderMapper.selectById(v.getPitOrderId());
                if (null != tPoWorkshopPitOrder) {
                    tPoWorkshopPitOrder.setVinasseId(dto.getOrder().getUpdateVinasseId());
                    tPoWorkshopPitOrder.setInPitNum(v.getTurnoverPotNum());
                    workshopPitOrderMapper.updateById(tPoWorkshopPitOrder);

                    //新增窖池订单执行状态
                    workshopPitOrderService.putExecutionStatus(tPoWorkshopPitOrder.getOrderCode(), ExecutionStatus.CELLAR_TURN.getStatus(), 9);

                    //结束PDA个人任务执行进度
                    taskProcessRecordsService.closeTask(dto.getOrder().getTurnoverOrderCode(), ExecutionStatus.CELLAR_TURN.getStatus());

                    List<TPoWorkshopPitOrderSap> tPoWorkshopPitOrderSaps = workshopPitOrderSapMapper.selectList(new LambdaQueryWrapper<TPoWorkshopPitOrderSap>()
                            .eq(TPoWorkshopPitOrderSap::getOrderCodeId, tPoWorkshopPitOrder.getId()));
                    TPoWorkshopFullPit fullPit = workshopFullPitMapper.selectById(tPoWorkshopPitOrder.getPitId());
                    log.info("调用翻窖任务完成-给sap传输数据");
                    //给spa传输翻窖后的数据-------------林远志写
                    for (TPoWorkshopPitOrderSap tPoWorkshopPitOrderSap : tPoWorkshopPitOrderSaps) {
                        BaseOrderMessageSapDTO messageSapDTO = new BaseOrderMessageSapDTO();
                        messageSapDTO.setId(tPoWorkshopPitOrderSap.getId());
                        messageSapDTO.setSapOrderCode(tPoWorkshopPitOrderSap.getOrderCode());
                        messageSapDTO.setFullPitId(fullPit.getFullPitId());
                        messageSapDTO.setUserId(userId);
                        messageSapDTO.setCenterId(tPoWorkshopPitOrder.getCenterId());
                        messageSapDTO.setSyncType(SapCancelType.TURN_OVER_INPUT);
                        messageSapDTO.setOperationTime(tPoWorkshopPitOrder.getTurnoverCount());
                        String headKey = MqQueueConstant.ORDER_TURNOVER_INPUT_SAP + "_" + MqQueueConstant.OPERATE_TYPE_SYNC + "_" + tPoWorkshopPitOrderSap.getId();
                        //发送同步请求到mq
                        MessageConsume messageConsume = AmqpTemplateUtil.init(MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, MqQueueConstant.ORDER_TURNOVER_INPUT_SAP, JSONObject.toJSONString(messageSapDTO), headKey, MqQueueConstant.OPERATE_TYPE_SYNC);
                        //消息投递
                        messageConsume.setPackageId(packageId);
                        messageConsumeClient.addMessage(messageConsume);
                        log.info("调用翻窖任务完成-给sap传输数据，参数--》{}",JSONObject.toJSONString(messageConsume));
                        AmqpTemplateUtil.sendMessageToMq(MqQueueConstant.ORDER_TURNOVER_INPUT_SAP, MqQueueConstant.EXCHANGE_SAP_BREWAGE_SERVER, JSONObject.toJSONString(messageConsume));
                    }
                }
            });


            // 修改单窖订单
            LambdaUpdateWrapper<TPoWorkshopPitOrderSap> update = new LambdaUpdateWrapper<>();
            update.in(TPoWorkshopPitOrderSap::getOrderCodeId, planList.stream().filter(p -> p.getTurnoverPotNum() != null).map(TPoWorkshopPitTurnoverOrderPlan::getPitOrderId).collect(Collectors.toList()))
                    .eq(TPoWorkshopPitOrderSap::getIsDeleted, false)
                    .set(TPoWorkshopPitOrderSap::getOrderStatus, 1)
                    .set(TPoWorkshopPitOrderSap::getTurnOverFinishTime, new Date());
            workshopPitOrderSapMapper.update(null, update);
        }

        //翻沙曲、回酒需要调用原辅料的接口，获取对应的批次信息
        //翻沙曲传：中心+车间+跨号+需求用量
        //回酒传：中心+车间+需求用量

        if (planList.size() > 0) {
            TPoWorkshopPitTurnoverOrder turnoverOrder = tPoWorkshopPitTurnoverOrderMapper.selectById(order.getId());

            //根据翻窖次数和翻窖糟源，查询翻窖的配方及对应的物料编码
            FJMaterialCodeVO fjMaterialCodeVO = tPoWorkshopPitTurnoverOrderMapper.selectFJMaterialCode(order.getVinasseId(), order.getUpdateVinasseName(), order.getTurnoverTimes());

            //组装数据
            List<TPoWorkshopPitTurnoverOrderPlan> orderPlanList = addPlanList(planList, order.getQuQuantity(), order.getBackAlcoholicQuantity());
            orderPlanList.forEach(item -> {
                //根据连窖订单查询单窖订单
                List<TPoWorkshopPitOrderSap> sapList = workshopPitOrderSapMapper.getOrderSapByOrderCodeId(item.getPitOrderId());
                if(sapList.size()>1){
                    int[] splitEvenly = splitEvenly(item.getTurnoverPotNum());
                    sapList.get(0).setInPotNumExc(splitEvenly[0]);
                    sapList.get(1).setInPotNumExc(splitEvenly[1]);
                }else if(sapList.size()==1) {
                    sapList.get(0).setInPotNumExc(item.getTurnoverPotNum());
                }

                sapList = addOrderSapList(sapList, item.getQuQuantity(), item.getBackAlcoholicQuantity());
                sapList.forEach(sap->{
                    if (item.getQuQuantity() > 0) {
                        TurnoverUseMaterialDTO fsqQueryDTO = new TurnoverUseMaterialDTO();
                        fsqQueryDTO.setCenterId(turnoverOrder.getCenterId());
                        fsqQueryDTO.setLocationId(turnoverOrder.getLocationId());
                        fsqQueryDTO.setUseCount(BigDecimal.valueOf(sap.getQuQuantity()));
                        //刚子说滴，随便取当前翻窖任务下面的窖池订单任意一个区域，都是一样的
                        fsqQueryDTO.setLineName(planList.get(0).getArea());
                        fsqQueryDTO.setType(OtherEnum.DQ);
                        fsqQueryDTO.setMaterialCode(fjMaterialCodeVO.getQfMaterialCode());
                        log.info("调用原辅料接口，查询翻沙曲对应的用量批次，传递参数：{}", JSONObject.toJSONString(fsqQueryDTO));
                        //开始调用接口
                        List<UseMaterialBatchDTO> useBatchDTOList = turnoverUseMaterial(fsqQueryDTO);
                        log.info("收到调用原辅料接口查询翻沙曲返回值，返回信息：{}", JSONObject.toJSONString(useBatchDTOList));
                        taskOrderBatchService.addTurnoverUseBatch(turnoverOrder.getTurnoverOrderCode(), ExecutionStatus.CELLAR_TURN.getStatus(), useBatchDTOList, sap.getOrderCode(),sap.getInPotNumExc(), OtherEnum.DQ,"2");
                    }

                    if (item.getBackAlcoholicQuantity() > 0) {
                        TurnoverUseMaterialDTO hjQueryDTO = new TurnoverUseMaterialDTO();
                        hjQueryDTO.setCenterId(turnoverOrder.getCenterId());
                        hjQueryDTO.setLocationId(turnoverOrder.getLocationId());
                        hjQueryDTO.setUseCount(BigDecimal.valueOf(sap.getBackAlcoholicQuantity()));
                        hjQueryDTO.setType(OtherEnum.HJ);
                        hjQueryDTO.setMaterialCode(fjMaterialCodeVO.getHjMaterialCode());
                        log.info("调用原辅料接口，查询回酒对应的用量批次，传递参数：{}", JSONObject.toJSONString(hjQueryDTO));
                        //开始调用接口
                        List<UseMaterialBatchDTO> batchDTOList = turnoverUseMaterial(hjQueryDTO);
                        log.info("收到调用原辅料接口查询回酒返回值，返回信息：{}", JSONObject.toJSONString(batchDTOList));
                        taskOrderBatchService.addTurnoverUseBatch(turnoverOrder.getTurnoverOrderCode(), ExecutionStatus.CELLAR_TURN.getStatus(), batchDTOList, sap.getOrderCode(),sap.getInPotNumExc(), OtherEnum.HJ,"2");
                    }
                });
            });
        }

        return AjaxResult.success();
    }

    /**
     * 将总数尽可能均匀地分成两部分
     *
     * @param total 总数
     * @return 包含两个分摊结果的数组
     */
    public static int[] splitEvenly(int total) {
        if (total < 0) {
            throw new IllegalArgumentException("总数不能为负数");
        }

        int firstPart = (total + 1) / 2; // 利用整数除法自动向下取整的特性
        int secondPart = total - firstPart;

        return new int[]{firstPart, secondPart};
    }

    /**
     * 组装数据
     *
     * @return
     */
    public List<TPoWorkshopPitTurnoverOrderPlan> addPlanList(List<TPoWorkshopPitTurnoverOrderPlan> planList, Double quQuantity, Double hjQuantity) {
        List<TPoWorkshopPitTurnoverOrderPlan> orderPlanList = planList.stream().filter(item -> item.getTurnoverPotNum() > 0).collect(Collectors.toList());
        // 按照占比分配
        double potSum = orderPlanList.stream().mapToDouble(TPoWorkshopPitTurnoverOrderPlan::getTurnoverPotNum).sum();
        for (TPoWorkshopPitTurnoverOrderPlan orderPlan : orderPlanList) {
            orderPlan.setQuQuantity(Double.parseDouble(new DecimalFormat("#").format(quQuantity * (orderPlan.getTurnoverPotNum() / potSum))));
            orderPlan.setBackAlcoholicQuantity(Double.parseDouble(new DecimalFormat("#").format(hjQuantity * (orderPlan.getTurnoverPotNum() / potSum))));

        }
        // 计算差值，后修正
        //这里只是为了将上一步计算的余数，添加到第一条数据里面
        double quSum = orderPlanList.stream().mapToDouble(TPoWorkshopPitTurnoverOrderPlan::getQuQuantity).sum();
        double backSum = orderPlanList.stream().mapToDouble(TPoWorkshopPitTurnoverOrderPlan::getBackAlcoholicQuantity).sum();
        TPoWorkshopPitTurnoverOrderPlan orderPlan = orderPlanList.get(0);

        orderPlan.setQuQuantity(orderPlan.getQuQuantity() + (quQuantity - quSum));
        orderPlan.setBackAlcoholicQuantity(orderPlan.getBackAlcoholicQuantity() + (hjQuantity - backSum));

        return orderPlanList;
    }

    /**
     * 组装数据
     *
     * @return
     */
    public List<TPoWorkshopPitOrderSap> addOrderSapList(List<TPoWorkshopPitOrderSap> sapList, Double quQuantity, Double hjQuantity) {
        List<TPoWorkshopPitOrderSap> orderSapList = sapList.stream().filter(item -> item.getInPotNumExc() > 0).collect(Collectors.toList());
        // 按照占比分配
        double potSum = orderSapList.stream().mapToDouble(TPoWorkshopPitOrderSap::getInPotNumExc).sum();
        for (TPoWorkshopPitOrderSap orderSap : orderSapList) {
            orderSap.setQuQuantity(Double.parseDouble(new DecimalFormat("#").format(quQuantity * (orderSap.getInPotNumExc() / potSum))));
            orderSap.setBackAlcoholicQuantity(Double.parseDouble(new DecimalFormat("#").format(hjQuantity * (orderSap.getInPotNumExc() / potSum))));

        }
        // 计算差值，后修正
        //这里只是为了将上一步计算的余数，添加到第一条数据里面
        double quSum = orderSapList.stream().mapToDouble(TPoWorkshopPitOrderSap::getQuQuantity).sum();
        double backSum = orderSapList.stream().mapToDouble(TPoWorkshopPitOrderSap::getBackAlcoholicQuantity).sum();
        TPoWorkshopPitOrderSap orderSap = orderSapList.get(0);

        orderSap.setQuQuantity(orderSap.getQuQuantity() + (quQuantity - quSum));
        orderSap.setBackAlcoholicQuantity(orderSap.getBackAlcoholicQuantity() + (hjQuantity - backSum));

        return orderSapList;
    }


    @Override
    public List<TPoWorkshopPitTurnoverOrderPlan> selectAllByTurnoverOrderId(Integer turnoverOrderId) {
        return turnoverOrderPlanMapper.selectAllByTurnoverOrderId(turnoverOrderId);
    }

    @Override
    public ExcelExportDto exportTurnoverTask(QueryPitTurnOverDTO dto) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter writer = EasyExcelFactory.write(outputStream)
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .registerWriteHandler(new TurnoverOrderHandler())
                .build();
        List<List<String>> head = head();
        // 动态添加表头，适用一些表头动态变化的场景
        WriteSheet sheet1 = new WriteSheet();
        sheet1.setSheetName("翻窖任务");
        sheet1.setSheetNo(0);
        // 创建一个表格，用于 Sheet 中使用
        WriteTable table = new WriteTable();
        table.setTableNo(0);
        table.setHead(head);

        writer.write(contentData(dto), sheet1, table);
        writer.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName("翻窖任务.xlsx");
        exportDto.setBody(outputStream.toByteArray());
        return exportDto;
    }

    private List<List<String>> head() {
        List<List<String>> headTitles = Lists.newArrayList();
        headTitles.add(Lists.newArrayList("任务号"));
        headTitles.add(Lists.newArrayList("中心"));
        headTitles.add(Lists.newArrayList("车间"));
        headTitles.add(Lists.newArrayList("班组"));
        headTitles.add(Lists.newArrayList("班次"));
        headTitles.add(Lists.newArrayList("糟源类型"));
        headTitles.add(Lists.newArrayList("入窖总甑口"));
        headTitles.add(Lists.newArrayList("翻沙曲配方量"));
        headTitles.add(Lists.newArrayList("回酒预配方量"));
        headTitles.add(Lists.newArrayList("翻沙曲使用量"));
        headTitles.add(Lists.newArrayList("回酒使用量"));
        headTitles.add(Lists.newArrayList("计划时间"));
        headTitles.add(Lists.newArrayList("实际完成时间"));
        headTitles.add(Lists.newArrayList("状态"));

        headTitles.add(Lists.newArrayList("糟醅来源窖池"));
        headTitles.add(Lists.newArrayList("窖池订单号"));
        headTitles.add(Lists.newArrayList("留底甑口"));
        headTitles.add(Lists.newArrayList("入窖甑口"));
        headTitles.add(Lists.newArrayList("翻出糟醅甑口"));
        headTitles.add(Lists.newArrayList("翻窖完成甑口"));
        headTitles.add(Lists.newArrayList("糟醅目的窖池"));
        headTitles.add(Lists.newArrayList("窖池订单"));
        headTitles.add(Lists.newArrayList("入窖甑口"));
        headTitles.add(Lists.newArrayList("翻入糟醅甑口"));
        headTitles.add(Lists.newArrayList("翻窖完成甑口"));
        headTitles.add(Lists.newArrayList("翻窖时间"));
        headTitles.add(Lists.newArrayList("翻窖人"));
        headTitles.add(Lists.newArrayList("翻沙曲"));
        headTitles.add(Lists.newArrayList("回酒"));

        for (List<String> headTitle : headTitles) {
            headTitle.add(0, "翻窖任务");
        }

        return headTitles;
    }

    private List<List<Object>> contentData(QueryPitTurnOverDTO dto) {
        List<List<Object>> contentList = Lists.newArrayList();
        dto.setPage(1);
        dto.setPageSize(Integer.MAX_VALUE);
        List<TPoWorkshopPitTurnoverOrder> data = getOrderByPage(dto).getRecords();
        for (TPoWorkshopPitTurnoverOrder datum : data) {
            String status = "";
            switch (datum.getOrderStatus()) {
                case 0: {
                    status = "新建";
                    break;
                }
                case 1: {
                    status = "已下发";
                    break;
                }
                case 2: {
                    status = "已完成";
                    break;
                }
            }
            List<TPoWorkshopPitTurnoverOrderDetail> tPoWorkshopPitTurnoverOrderDetails = detailService.selectAllData(datum.getTurnoverOrderCode());
            if (tPoWorkshopPitTurnoverOrderDetails.size() > 0) {
                List<Double> detailQuList = averageQuality(datum.getQuQuantity(), tPoWorkshopPitTurnoverOrderDetails.size());
                List<Double> detailBackAlcoholicList = averageQuality(datum.getBackAlcoholicQuantity(), tPoWorkshopPitTurnoverOrderDetails.size());
                for (int i = 0; i < tPoWorkshopPitTurnoverOrderDetails.size(); i++) {
                    TPoWorkshopPitTurnoverOrderDetail tPoWorkshopPitTurnoverOrderDetail = tPoWorkshopPitTurnoverOrderDetails.get(i);
                    List<Object> objects = Lists.newArrayList();
                    objects.add(datum.getTurnoverOrderCode());
                    objects.add(datum.getCentreName());
                    objects.add(datum.getLocationName());
                    objects.add(datum.getCrewName());
                    objects.add(datum.getShiftName());
                    objects.add(datum.getVinasseName());
                    objects.add(datum.getPotsCount());
                    objects.add(datum.getQuQuantityPlan());
                    objects.add(datum.getBackAlcoholicQuantityPlan());
                    objects.add(datum.getQuQuantity());
                    objects.add(datum.getBackAlcoholicQuantity());
                    objects.add(datum.getPlanDate() == null ? "" : datum.getPlanDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    objects.add(datum.getActualEndtime() == null ? "" : datum.getActualEndtime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    objects.add(status);

                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getPitCode());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getPitOrderCode());
                    objects.add(datum.getRemainNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getPotsNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getOutPotNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getOutPotFinishNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getFormPitCode());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getFromPitOrderCode());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getInPotNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getaoutPotNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getInPotFinishNum());
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getTurnoverTime() == null ? "" : tPoWorkshopPitTurnoverOrderDetail.getTurnoverTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                    objects.add(tPoWorkshopPitTurnoverOrderDetail.getUserName());
                    objects.add(detailQuList.get(i));
                    objects.add(detailBackAlcoholicList.get(i));

                    contentList.add(objects);
                }
            } else {
                List<Object> objects = Lists.newArrayList();
                objects.add(datum.getTurnoverOrderCode());
                objects.add(datum.getCentreName());
                objects.add(datum.getLocationName());
                objects.add(datum.getCrewName());
                objects.add(datum.getShiftName());
                objects.add(datum.getVinasseName());
                objects.add(datum.getPotsCount());
                objects.add(datum.getQuQuantityPlan());
                objects.add(datum.getBackAlcoholicQuantityPlan());
                objects.add(datum.getQuQuantity());
                objects.add(datum.getBackAlcoholicQuantity());
                objects.add(datum.getPlanDate() == null ? "" : datum.getPlanDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                objects.add(datum.getActualEndtime() == null ? "" : datum.getActualEndtime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                objects.add(status);

                for (int i = 0; i < 13; i++) {
                    objects.add("");
                }

                contentList.add(objects);
            }

        }
        return contentList;
    }

    /**
     * 均分重量，去尾数
     *
     * @return
     */
    private List<Double> averageQuality(Double quality, Integer count) {
        List<Double> list = new ArrayList<Double>();

        if (quality == null) {
            for (Integer i = 0; i < count; i++) {
                list.add(0d);
            }
            return list;
        }

        if (quality % count > 0) {
            for (Integer i = 0; i < count; i++) {
                if (i == 0) {
                    list.add((double) (int) (quality / count) + (quality % count));
                } else {
                    list.add((double) (int) (quality / count));
                }
            }
        } else {
            for (Integer i = 0; i < count; i++) {
                list.add((double) (int) (quality / count));
            }
        }
        return list;
    }

    private void sendMessageToSap(List<TPoWorkshopPitTurnoverOrderDetail> detailItems
            , List<TPoWorkshopPitOrderSap> tPoWorkshopPitOrderSaps
            , List<ChoosePitOrderVO> inPutCountByOrderCode
            , TechnologicalRequirementsVO data
            , List<TPoWorkshopPitTurnoverOrderPlan> planList) {
        QueryUserByRoleDTO roleUserDTO = new QueryUserByRoleDTO();
        roleUserDTO.setCenterId(-1);
        roleUserDTO.setLocationId(-1);
        roleUserDTO.setList(Lists.newArrayList("生产管理部SAP"));
        List<UserVO> userRole = commonService.getCenterPeopleByUserRole(roleUserDTO);

        if (!CollectionUtils.isEmpty(userRole)) {

            //1、对于翻出甑口，偶数平均，奇数一个平均值取整，一个进1，然后扣减单窖最新甑口数
            //2、对于翻入甑口，，偶数平均，奇数一个平均值取整，一个进1，然后累加单窖最新甑口数
            for (TPoWorkshopPitTurnoverOrderDetail item : detailItems) {

                if (item.getOutPotNum() == null) {
                    continue;
                }

                Integer inPotNum = item.getOutPotNum();
                Integer oneNum = inPotNum / 2;
                Integer secondNum = BigDecimal.valueOf(inPotNum / (double) 2).setScale(0, RoundingMode.UP).intValue();

                TPoWorkshopPitTurnoverOrderPlan firstData = planList.stream().filter(p -> p.getId().equals(item.getFirstPitDataId())).findFirst().orElse(null);
                TPoWorkshopPitTurnoverOrderPlan secondData = planList.stream().filter(p -> p.getId().equals(item.getSecondPitDataId())).findFirst().orElse(null);

                if (firstData == null || secondData == null) {
                    continue;
                }

                // 翻出窖池
                List<TPoWorkshopPitOrderSap> outCollect = tPoWorkshopPitOrderSaps.stream().filter(p -> p.getOrderCodeId().equals(firstData.getPitOrderId())).collect(Collectors.toList());
                for (int i = 0; i < 2; i++) {
                    int finalIndex = i;
                    inPutCountByOrderCode.stream().filter(p -> p.getOrderCode().equals(outCollect.get(finalIndex).getOrderCode())).findFirst().ifPresent(p -> {
                        if (finalIndex == 0) {
                            p.setInPitNum(getPotNum(p.getInPitNum(), oneNum, false));
                        } else {
                            p.setInPitNum(getPotNum(p.getInPitNum(), secondNum, false));
                        }
                    });
                }

                // 翻入窖池
                List<TPoWorkshopPitOrderSap> inCollect = tPoWorkshopPitOrderSaps.stream().filter(p -> p.getOrderCodeId().equals(secondData.getPitOrderId())).collect(Collectors.toList());
                for (int i = 0; i < 2; i++) {
                    int finalIndex = i;
                    inPutCountByOrderCode.stream().filter(p -> p.getOrderCode().equals(inCollect.get(finalIndex).getOrderCode())).findFirst().ifPresent(p -> {
                        if (finalIndex == 0) {
                            p.setInPitNum(getPotNum(p.getInPitNum(), oneNum, true));
                        } else {
                            p.setInPitNum(getPotNum(p.getInPitNum(), secondNum, true));
                        }
                    });
                }

            }

            //  对生产管理部SAP角色对应用户的消息推送功能 通知 窖池订单号：XXXX SAP订单号:XXX 窖号:XXX 糟源类别：XXX 入窖甑口：XXX 封窖日期:XXX
            for (TPoWorkshopPitOrderSap pitOrderSap : tPoWorkshopPitOrderSaps) {
                ChoosePitOrderVO choosePitOrderVO = inPutCountByOrderCode.stream().filter(p -> p.getOrderCode().equals(pitOrderSap.getOrderCode())).findFirst().orElse(null);
                String content = String.format("窖池订单号：%s SAP订单号：%s 窖号：%s 糟源类别：%s 入窖甑口：%s 封窖日期：%s",
                        pitOrderSap.getOrderCode(),
                        getNullString(pitOrderSap.getSapOrderCode()),
                        getNullString(pitOrderSap.getPitCode()),
                        getNullString(data.getNewVinasse()),
                        choosePitOrderVO == null ? 0 : choosePitOrderVO.getInPitNum(),
                        pitOrderSap.getSealConfirmTime() == null ? "" : DateUtil.dateFormat(pitOrderSap.getSealConfirmTime())
                );
                messageService.sendMessageByUser(pitOrderSap.getOrderCode(), content, userRole.stream().map(UserVO::getId).collect(Collectors.toList()));
            }

        }
    }

    /**
     * 修改翻窖计划窖池数据
     *
     * @param collect
     * @param detail
     * @return
     */
    private List<TPoWorkshopPitTurnoverOrderPlan> modifyPlanData(List<TPoWorkshopPitTurnoverOrderPlan> collect, TPoWorkshopPitTurnoverOrderDetail detail) {
        List<TPoWorkshopPitTurnoverOrderPlan> list = new ArrayList<>();

        for (TPoWorkshopPitTurnoverOrderPlan plan : collect) {

            if (plan.getId().equals(detail.getFirstPitDataId())) {
                plan.setTurnoverPotNum(getPotNum(plan.getTurnoverPotNum(), detail.getOutPotNum(), false));
                // 防止串改原始数据
                plan.setIntPotNum(null);
                list.add(plan);
            }

            if (plan.getId().equals(detail.getSecondPitDataId())) {
                plan.setTurnoverPotNum(getPotNum(plan.getTurnoverPotNum(), detail.getOutPotNum(), true));
                // 防止串改原始数据
                plan.setIntPotNum(null);
                list.add(plan);
            }
        }

        return list;
    }


    /**
     * 重置计划窖池数据、窖池明细数据
     */
    private void resetTurnoverData(List<TPoWorkshopPitTurnoverOrderPlan> pitOrderPlans, List<TPoWorkshopPitTurnoverOrderDetail> orderDetails) {

        if (orderDetails.size() == 0) {
            pitOrderPlans.forEach(p -> {
                p.setTurnoverPotNum(null);
            });
            return;
        }


        // 将数据的翻窖数全部赋值为入甑甑口数，这样下面会好写
        pitOrderPlans.forEach(p -> {
            p.setIsUsed(false);
            p.setTurnoverPotNum(p.getIntPotNum());
        });

        // 根据窖池订单id分组
        //Map<Integer, List<TPoWorkshopPitTurnoverOrderPlan>> collect = pitOrderPlans.stream().collect(Collectors.groupingBy(TPoWorkshopPitTurnoverOrderPlan::getId));

        for (TPoWorkshopPitTurnoverOrderDetail orderDetail : orderDetails) {

            TPoWorkshopPitTurnoverOrderPlan fromOrderData = null;
            TPoWorkshopPitTurnoverOrderPlan toOrderData = null;

            for (TPoWorkshopPitTurnoverOrderPlan orderPlan : pitOrderPlans) {

                if (fromOrderData != null && toOrderData != null) {
                    break;
                }

                if (orderPlan.getId().equals(orderDetail.getFirstPitDataId())) {
                    fromOrderData = orderPlan;
                }

                if (orderPlan.getId().equals(orderDetail.getSecondPitDataId())) {
                    toOrderData = orderPlan;
                }
            }

            if (fromOrderData == null || toOrderData == null) {
                throw new BaseKnownException(10000, "翻窖任务包含未知窖池订单数据");
            }

            if (orderDetail.getIsDelete() != 0) {
                if (fromOrderData.getIsUsed() == null || Boolean.FALSE.equals(fromOrderData.getIsUsed())) {
                    fromOrderData.setTurnoverPotNum(null);
                }
                fromOrderData.setIsUsed(true);

                if (toOrderData.getIsUsed() == null || Boolean.FALSE.equals(toOrderData.getIsUsed())) {
                    toOrderData.setTurnoverPotNum(null);
                }
                toOrderData.setIsUsed(true);
                continue;
            }

            // =================== 修改来源甑口 ===================
            orderDetail.setPitId(fromOrderData.getPitId());
            orderDetail.setPitCode(fromOrderData.getPitCode());
            orderDetail.setOrderCodeId(fromOrderData.getPitOrderId());
            orderDetail.setPitOrderCode(fromOrderData.getPitOrderCode());

            orderDetail.setPotsNum(fromOrderData.getTurnoverPotNum() == null ? fromOrderData.getIntPotNum() : fromOrderData.getTurnoverPotNum());
            // 翻出完成甑口数 = 入甑-翻出
            orderDetail.setOutPotFinishNum(getPotNum(orderDetail.getPotsNum(), orderDetail.getOutPotNum(), false));
            // 将计划窖池的翻窖甑口设置为翻出甑口
            fromOrderData.setTurnoverPotNum(orderDetail.getOutPotFinishNum());
            fromOrderData.setIsUsed(true);

            // =================== 修改目的甑口 ===================
            orderDetail.setFromPitId(toOrderData.getPitId());
            orderDetail.setFormPitCode(toOrderData.getPitCode());
            orderDetail.setFromOrderCodeId(toOrderData.getPitOrderId());
            orderDetail.setFromPitOrderCode(toOrderData.getPitOrderCode());

            // 目的窖池原始入窖甑口
            orderDetail.setInPotNum(toOrderData.getTurnoverPotNum() == null ? toOrderData.getIntPotNum() : toOrderData.getTurnoverPotNum());
            // 翻入完成甑口 = 入甑+翻入
            orderDetail.setInPotFinishNum(getPotNum(orderDetail.getInPotNum(), orderDetail.getOutPotNum(), true));
            // 将计划窖池的翻窖甑口设置为翻出甑口
            toOrderData.setTurnoverPotNum(orderDetail.getInPotFinishNum());
            toOrderData.setIsUsed(true);

        }
    }

    private int getPotNum(Integer firstData, Integer value, boolean isAdd) {

        int dataInt = firstData == null ? 0 : firstData;
        int valueInt = value == null ? 0 : value;


        return isAdd ? (dataInt + valueInt) : (dataInt - valueInt);

    }


    private String getNullString(String str) {
        return StringUtils.isEmpty(str) ? "" : str;
    }


    /**
     * 翻窖使用物料 --返回使用的批次和使用明细
     *
     * @param useMaterialDTO
     * @return
     */
    @Override
    @Transactional
    public List<UseMaterialBatchDTO> turnoverUseMaterial(TurnoverUseMaterialDTO useMaterialDTO) {
        if (useMaterialDTO.getUseCount() == null || useMaterialDTO.getUseCount().compareTo(BigDecimal.ZERO) == 0) {
            throw new BaseKnownException(1000, "使用的量不能为空");
        }
        String inventoryLock = "inventory_" + useMaterialDTO.getType();
        try {
            if (RedissonLockUtil.tryLock(inventoryLock, 15L, 25L, TimeUnit.SECONDS)) {
                if ("大曲".equals(useMaterialDTO.getType())) {
                    return useQuUseData(useMaterialDTO);
                } else if ("回酒".equals(useMaterialDTO.getType())) {
                    return useHjUseData(useMaterialDTO);
                } else if ("高粱".equals(useMaterialDTO.getType())) {
                    return useGLUseData(useMaterialDTO);
                } else if ("稻壳".equals(useMaterialDTO.getType())) {
                    return useDKUseData(useMaterialDTO);
                } else {
                    throw new BaseKnownException(1000, "物料数据有误，物料使用失败");
                }
            } else {
                throw new BaseKnownException(10000, "同步失败,数据:{}已经被锁定：", inventoryLock);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            RedissonLockUtil.unlock(inventoryLock);
        }
    }

    @Override
    public Page<TPoWorkshopPitTurnoverOrderVO> getPitTurnoverOrder(WorkshopPitTurnoverOrderQueryDTO queryDTO) {
        Page<TPoWorkshopPitTurnoverOrderVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        List<TPoWorkshopPitTurnoverOrderVO> turnoverOrderList = tPoWorkshopPitTurnoverOrderMapper.getPitTurnoverOrder(page, queryDTO);
        page.setRecords(turnoverOrderList);
        return page;
    }

    /**
     * 稻壳数据使用
     *
     * @param useMaterialDTO
     * @return
     */
    private List<UseMaterialBatchDTO> useDKUseData(TurnoverUseMaterialDTO useMaterialDTO) {
        BigDecimal useCount = useMaterialDTO.getUseCount();
        List<UseMaterialBatchDTO> batchDTOS = new ArrayList<>();
        //获取车间稻壳数据
        List<UseMaterialQueryBatchDTO> dkBatchDTOS = tPoWorkshopPitTurnoverOrderMapper.selectLocationDKBatchList(useMaterialDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(dkBatchDTOS)) {
            throw new BaseKnownException(1000, "当前稻壳库存余量不足，扣除失败");
        }
        //因为高粱是批次信息是统一的，直接取最新发放的批次
        UseMaterialQueryBatchDTO dkBatchDTO = dkBatchDTOS.get(0);
        UseMaterialBatchDTO batchDTO = new UseMaterialBatchDTO();
        batchDTO.setBatch(dkBatchDTO.getBatch());
        batchDTO.setUseCount(useCount);
        batchDTO.setTaskNo(dkBatchDTO.getTaskNo());
        batchDTO.setMaterialCode(dkBatchDTO.getMaterialCode());
        batchDTO.setMaterialName(dkBatchDTO.getMaterialName());
        batchDTO.setMaterielId(dkBatchDTO.getMaterielId());
        batchDTOS.add(batchDTO);
        return batchDTOS;
    }

    /**
     * 高粱数据使用
     *
     * @param useMaterialDTO
     * @return
     */
    private List<UseMaterialBatchDTO> useGLUseData(TurnoverUseMaterialDTO useMaterialDTO) {
        BigDecimal useCount = useMaterialDTO.getUseCount();
        List<UseMaterialBatchDTO> batchDTOS = new ArrayList<>();
        //获取车间高粱数据
        List<UseMaterialQueryBatchDTO> glBatchDTOS = tPoWorkshopPitTurnoverOrderMapper.selectLocationGLBatchList(useMaterialDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(glBatchDTOS)) {
            throw new BaseKnownException(1000, "当前高粱库存余量不足，扣除失败");
        }
        //因为高粱是批次信息是统一的，直接取最新发放的批次
        UseMaterialQueryBatchDTO glBatchDTO = glBatchDTOS.get(0);
        UseMaterialBatchDTO batchDTO = new UseMaterialBatchDTO();
        batchDTO.setBatch(glBatchDTO.getBatch());
        batchDTO.setUseCount(useCount);
        batchDTO.setTaskNo(glBatchDTO.getTaskNo());
        batchDTO.setMaterialCode(glBatchDTO.getMaterialCode());
        batchDTO.setMaterialName(glBatchDTO.getMaterialName());
        batchDTO.setMaterielId(glBatchDTO.getMaterielId());
        batchDTOS.add(batchDTO);
        return batchDTOS;
    }

    /**
     * 大曲数据使用
     *
     * @param useMaterialDTO
     */
    private List<UseMaterialBatchDTO> useQuUseData(TurnoverUseMaterialDTO useMaterialDTO) {
        List<UseMaterialBatchDTO> batchDTOList = useMaterialDTO.getBatchDTOList();
        if (!org.springframework.util.CollectionUtils.isEmpty(batchDTOList)) {
            TurnoverUseMaterialDTO query = new TurnoverUseMaterialDTO();
            query.setMaterialCode(useMaterialDTO.getMaterialCode());
            query.setInStock(false);
            query.setLocationId(useMaterialDTO.getLocationId());
            query.setCenterId(useMaterialDTO.getCenterId());
            query.setLineName(useMaterialDTO.getLineName());
            //库存增加，去掉上一次的数据，再重新扣数据
            for (UseMaterialBatchDTO batchDTO : batchDTOList) {
                query.setDataId(batchDTO.getDataId());
                UseMaterialQueryBatchDTO lastDto = tPoWorkshopPitTurnoverOrderMapper.selectLocationQuBatchList(query).get(0);
                BigDecimal updateQuantity = lastDto.getRemainQuantity().add(batchDTO.getUseCount());
                tPoWorkshopPitTurnoverOrderMapper.updateQuRemainQuantity(lastDto.getId(), updateQuantity);
                log.info("大曲批次追加库存:{}, 批次:{}", batchDTO.getUseCount(), batchDTO.getBatch());
            }
        }
        BigDecimal useCount = useMaterialDTO.getUseCount();
        List<UseMaterialBatchDTO> batchDTOS = new ArrayList<>();
        //获取车间大曲数据
        List<UseMaterialQueryBatchDTO> quBatchDTOS = tPoWorkshopPitTurnoverOrderMapper.selectLocationQuBatchList(useMaterialDTO);
        if (org.springframework.util.CollectionUtils.isEmpty(quBatchDTOS)) {
            throw new BaseKnownException(1000, "当前大曲库存余量不足，扣除失败");
        }
        //对余量进行倒扣
        for (UseMaterialQueryBatchDTO quBatchDTO : quBatchDTOS) {
            boolean jumpOut = false;
            BigDecimal useQuantity;
            if (useCount.compareTo(quBatchDTO.getRemainQuantity()) > 0) {
                //需求量大于余量的情况
                useCount = useCount.subtract(quBatchDTO.getRemainQuantity());
                useQuantity = quBatchDTO.getRemainQuantity();
                tPoWorkshopPitTurnoverOrderMapper.updateQuRemainQuantity(quBatchDTO.getId(), BigDecimal.ZERO);
                log.info("大曲批次扣除库存，余量不足，库存清零, 批次:{}", quBatchDTO.getBatch());
            } else {
                //需求量能够一次性扣除完成的情况
                useQuantity = useCount;
                BigDecimal remainQuantity = quBatchDTO.getRemainQuantity().subtract(useCount);
                tPoWorkshopPitTurnoverOrderMapper.updateQuRemainQuantity(quBatchDTO.getId(), remainQuantity);
                log.info("大曲批次扣除库存:{}, 批次:{}", useCount, quBatchDTO.getBatch());
                jumpOut = true;
            }
            UseMaterialBatchDTO batchDTO = new UseMaterialBatchDTO();
            batchDTO.setBatch(quBatchDTO.getBatch());
            batchDTO.setRequireEquipment(quBatchDTO.getRequireEquipment());
            batchDTO.setUseCount(useQuantity);
            batchDTO.setTaskNo(quBatchDTO.getTaskNo());
            batchDTO.setMaterialCode(quBatchDTO.getMaterialCode());
            batchDTO.setMaterialName(quBatchDTO.getMaterialName());
            batchDTO.setMaterielId(quBatchDTO.getMaterielId());
            batchDTO.setDataId(quBatchDTO.getDataId());
            batchDTOS.add(batchDTO);
            if (jumpOut) break;
        }
        return batchDTOS;
    }

    /**
     * 回酒数据使用
     *
     * @param useMaterialDTO
     */
    private List<UseMaterialBatchDTO> useHjUseData(TurnoverUseMaterialDTO useMaterialDTO) {
        List<UseMaterialBatchDTO> batchDTOList = useMaterialDTO.getBatchDTOList();
        if (!org.springframework.util.CollectionUtils.isEmpty(batchDTOList)) {
            TurnoverUseMaterialDTO query = new TurnoverUseMaterialDTO();
            query.setMaterialCode(useMaterialDTO.getMaterialCode());
            query.setInStock(false);
            query.setLocationId(useMaterialDTO.getLocationId());
            query.setCenterId(useMaterialDTO.getCenterId());
            query.setLineName(useMaterialDTO.getLineName());
            //库存增加，去掉上一次的数据，再重新扣数据
            for (UseMaterialBatchDTO batchDTO : batchDTOList) {
                TaskDetail queryData = taskDetailMapper.selectById(batchDTO.getDataId());
                BigDecimal updateQuantity = queryData.getRemainQuantity().add(batchDTO.getUseCount());
                log.info("回酒批次追加库存:{}, 批次:{}", batchDTO.getUseCount(), batchDTO.getBatch());
                tPoWorkshopPitTurnoverOrderMapper.updateQuRemainQuantity(queryData.getId(), updateQuantity);
            }
        }
        BigDecimal useCount = useMaterialDTO.getUseCount();
        List<UseMaterialBatchDTO> batchDTOS = new ArrayList<>();
        //回酒发放记录数据
        List<TaskDetail> taskDetails = taskDetailMapper.selectList(new LambdaUpdateWrapper<TaskDetail>()
                .eq(TaskDetail::getCenterId, useMaterialDTO.getCenterId())
                .eq(TaskDetail::getLocationId, useMaterialDTO.getLocationId())
                .eq(TaskDetail::getMaterialCode, useMaterialDTO.getMaterialCode())
                .ge(TaskDetail::getRemainQuantity, 0)
                .orderByAsc(TaskDetail::getId));
        if (org.springframework.util.CollectionUtils.isEmpty(taskDetails)) {
            throw new BaseKnownException(1000, "当前回酒库存余量不足，扣除失败");
        }
        //对余量进行倒扣
        for (TaskDetail taskDetail : taskDetails) {
            boolean jumpOut = false;
            BigDecimal useQuantity;
            if (useCount.compareTo(taskDetail.getRemainQuantity()) > 0) {
                //需求量大于余量的情况
                useCount = useCount.subtract(taskDetail.getRemainQuantity());
                useQuantity = taskDetail.getRemainQuantity();
                log.info("回酒批次库存余量不足，清空库存，批次:{}", taskDetail.getDetailBatch());
                taskDetailMapper.update(null, new LambdaUpdateWrapper<TaskDetail>()
                        .set(TaskDetail::getRemainQuantity, 0).eq(TaskDetail::getId, taskDetail.getId()));
            } else {
                //需求量能够一次性扣除完成的情况
                useQuantity = useCount;
                BigDecimal remainQuantity = taskDetail.getRemainQuantity().subtract(useCount);
                log.info("回酒批次扣除库存:{}, 批次:{}", taskDetail.getDetailBatch(), useCount);
                taskDetailMapper.update(null, new LambdaUpdateWrapper<TaskDetail>()
                        .set(TaskDetail::getRemainQuantity, remainQuantity).eq(TaskDetail::getId, taskDetail.getId()));
                jumpOut = true;
            }
            UseMaterialBatchDTO batchDTO = new UseMaterialBatchDTO();
            batchDTO.setDataId(taskDetail.getId());
            batchDTO.setBatch(taskDetail.getDetailBatch());
            batchDTO.setRequireEquipment(taskDetail.getRequireEquipment());
            batchDTO.setUseCount(useQuantity);
            batchDTO.setMaterielId(taskDetail.getMaterialId());
            batchDTO.setMaterialName(taskDetail.getMaterial());
            batchDTO.setMaterialCode(taskDetail.getMaterialCode());
            if (taskDetail.getTaskId() != null) {
                Task task = taskMapper.selectById(taskDetail.getTaskId());
                batchDTO.setTaskNo(task != null ? task.getTaskOrder() : "");
            }
            batchDTOS.add(batchDTO);
            if (jumpOut) break;
        }
        return batchDTOS;
    }

}

