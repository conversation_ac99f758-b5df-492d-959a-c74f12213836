package com.hvisions.brewage.mkwine.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.handler.AbstractRowWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.fastjson.JSONObject;
import com.hvisions.brewage.dao.tpo.TaskSpreadingMapper;
import com.hvisions.brewage.dto.PoolTemperatureDTO;
import com.hvisions.brewage.dto.tpo.TaskTemperatureDetailAddDTO;
import com.hvisions.brewage.enums.TaskBusiness;
import com.hvisions.brewage.mkwine.ReportHandler.HeadAndContentCellStyle;
import com.hvisions.brewage.mkwine.dao.FermentationManagement.WorkshopPitTemperatureTaskMapper;
import com.hvisions.brewage.mkwine.dto.FermentationManagement.WorkshopPitTemperatureTask.*;
import com.hvisions.brewage.mkwine.dto.ProductionProcess.WorkshopPitOrderOutAppraise.WorkshopPitOrderDTO;
import com.hvisions.brewage.mkwine.dto.WorkshopPit.VPotTaskAmountDTO;
import com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoWorkshopPitTemperatureTask;
import com.hvisions.brewage.mkwine.entity.FermentationManagement.TPoWorkshopPitTemperatureTaskItems;
import com.hvisions.brewage.mkwine.enums.ResultTipEnum;
import com.hvisions.brewage.mkwine.service.WorkshopPitTemperatureTaskService;
import com.hvisions.brewage.mkwine.vo.WorkshopPitCheckTask.*;
import com.hvisions.brewage.dto.mkwine.vo.PageVO;
import com.hvisions.brewage.repository.TPoWorkshopPitTemperatureTaskItemsRepository;
import com.hvisions.brewage.repository.TPoWorkshopPitTemperatureTaskRepository;
import com.hvisions.brewage.service.tpo.TaskTemperatureDetailService;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.MathUtils;
import com.hvisions.brewage.utils.cellmerge.CustomMergeCellHandler;
import com.hvisions.brewage.utils.cellmerge.MergeCellInfoInit;
import com.hvisions.brewage.utils.cellmerge.MergeCellModel;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.log.capture.client.LogCaptureClient;
import com.hvisions.log.capture.common.dto.LogDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: brewage
 * @description:
 * @author: DengWeiTao
 **/
@Slf4j
@Service
public class WorkshopPitTemperatureTaskServiceImpl implements WorkshopPitTemperatureTaskService {

    @Autowired
    private LogCaptureClient logCaptureClient;

    @Autowired
    private WorkshopPitTemperatureTaskMapper workshopPitTemperatureTaskMapper;

    @Resource
    TPoWorkshopPitTemperatureTaskItemsRepository pitTemperatureTaskItemsRepository;

    @Resource
    TPoWorkshopPitTemperatureTaskRepository pitTemperatureTaskRepository;

    @Resource
    private TaskSpreadingMapper taskSpreadingMapper;

    @Resource
    private TaskTemperatureDetailService taskTemperatureDetailService;

    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public synchronized Boolean insertTemperatureTask(InsertTemperatureTaskDTO insertTemperatureTaskDTO) {

        // 现在改成 记录日期 = 封窖日期 + 1
        if (insertTemperatureTaskDTO.getSealConfirmDate() == null) {
            throw new RuntimeException("封窖确认时间为空，新增窖池升温任务失败");
        }

        // 发酵天数
        int fermentationDays = DateUtil.subtractTwoDates(insertTemperatureTaskDTO.getSealConfirmDate(), new Date());

        // 根据 pit_id 获取甑口任务计算各个质量
        float fermentativeMaterialQuantity = 0f;
        float crushedGrainsQuantity = 0f;
        float ricehullQuantity = 0f;
        float waterProportioningQuantity = 0f;
        float quQuantity = 0f;

        // 这里是通过 连窖id 到 连窖表中找到单窖号，再通过单窖号关联甑口任务中的入窖号查找两条列表（因为目前为止 2022/5/27 一个连窖对应的是两个单窖），将质量相加
        List<WorkshopPitTemperatureTaskVO> workshopPitTemperatureTaskVOS =
                workshopPitTemperatureTaskMapper.calculateQuality(insertTemperatureTaskDTO.getPitId());

        for (WorkshopPitTemperatureTaskVO workshopPitTemperatureTaskVO : workshopPitTemperatureTaskVOS) {
            fermentativeMaterialQuantity += workshopPitTemperatureTaskVO.getFermentativeMaterialQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getFermentativeMaterialQuantity();
            crushedGrainsQuantity += workshopPitTemperatureTaskVO.getCrushedGrainsQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getCrushedGrainsQuantity();
            ricehullQuantity += workshopPitTemperatureTaskVO.getRicehullQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getRicehullQuantity();
            waterProportioningQuantity += workshopPitTemperatureTaskVO.getWaterProportioningQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getWaterProportioningQuantity();
            quQuantity += workshopPitTemperatureTaskVO.getQuQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getQuQuantity();
        }

        if (workshopPitTemperatureTaskMapper.insertTemperatureTask(
                new WorkshopPitTemperatureTaskDTO(
                        null,
                        insertTemperatureTaskDTO.getCenterId(),
                        insertTemperatureTaskDTO.getLocationId(),
                        insertTemperatureTaskDTO.getPitId(),
                        insertTemperatureTaskDTO.getVinasseId(),
                        fermentativeMaterialQuantity,
                        crushedGrainsQuantity,
                        ricehullQuantity,
                        waterProportioningQuantity,
                        quQuantity,
                        insertTemperatureTaskDTO.getSealConfirmDate(),
                        fermentationDays,
                        1,
                        insertTemperatureTaskDTO.getCrewId(),
                        insertTemperatureTaskDTO.getGroundTemperature(),
                        insertTemperatureTaskDTO.getInPitTemperature(),
                        null,
                        null,
                        null,
                        insertTemperatureTaskDTO.getPitOrderCode(),
                        insertTemperatureTaskDTO.getLayer(),
                        0
                ))) {
            // 新增明细
            // 记录日期 = 入窖日期 + 1
            //insertTemperatureTaskDTO.setInPitDate(DateUtil.datePlus(1, insertTemperatureTaskDTO.getInPitDate()));

            insertTemperatureTaskDTO.setInPitDate(DateUtil.datePlus(1, insertTemperatureTaskDTO.getSealConfirmDate()));
            int maxId = workshopPitTemperatureTaskMapper.getMaxId();
            return workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                    maxId,
                    insertTemperatureTaskDTO.getInPitDate(),
                    1
            ));
        }

        return false;
    }

    /**
     * 只新增任务，不新增明细
     *
     * @param insertTemperatureTaskDTO
     * @return
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public synchronized Boolean insertTemperatureTaskNew(InsertTemperatureTaskDTO insertTemperatureTaskDTO) {

        // 现在改成 记录日期 = 封窖日期 + 1
        if (insertTemperatureTaskDTO.getSealConfirmDate() == null) {
            throw new RuntimeException("封窖确认时间为空，新增窖池升温任务失败");
        }

        // 发酵天数
        int fermentationDays = DateUtil.subtractTwoDates(insertTemperatureTaskDTO.getSealConfirmDate(), new Date());

        // 根据 pit_id 获取甑口任务计算各个质量
        float fermentativeMaterialQuantity = 0f;
        float crushedGrainsQuantity = 0f;
        float ricehullQuantity = 0f;
        float waterProportioningQuantity = 0f;
        float quQuantity = 0f;

//        // 这里是通过 连窖id 到 连窖表中找到单窖号，再通过单窖号关联甑口任务中的入窖号查找两条列表（因为目前为止 2022/5/27 一个连窖对应的是两个单窖），将质量相加
//        List<WorkshopPitTemperatureTaskVO> workshopPitTemperatureTaskVOS =
//                workshopPitTemperatureTaskMapper.calculateQuality(insertTemperatureTaskDTO.getPitId());
        // 通过 窖池大订单号 找到 窖池小订单，再根据小订单号匹配入窖关联订单取得甑口任务，将质量相加
        List<WorkshopPitTemperatureTaskVO> workshopPitTemperatureTaskVOS =
                workshopPitTemperatureTaskMapper.calculateQualityNew(insertTemperatureTaskDTO.getPitOrderCode());

        for (WorkshopPitTemperatureTaskVO workshopPitTemperatureTaskVO : workshopPitTemperatureTaskVOS) {
            if (workshopPitTemperatureTaskVO == null)
                workshopPitTemperatureTaskVO = new WorkshopPitTemperatureTaskVO();
            fermentativeMaterialQuantity += workshopPitTemperatureTaskVO.getFermentativeMaterialQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getFermentativeMaterialQuantity();
            crushedGrainsQuantity += workshopPitTemperatureTaskVO.getCrushedGrainsQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getCrushedGrainsQuantity();
            ricehullQuantity += workshopPitTemperatureTaskVO.getRicehullQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getRicehullQuantity();
            waterProportioningQuantity += workshopPitTemperatureTaskVO.getWaterProportioningQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getWaterProportioningQuantity();
            quQuantity += workshopPitTemperatureTaskVO.getQuQuantity() == null ? 0f : workshopPitTemperatureTaskVO.getQuQuantity();
        }

        //新增之前，将窖池相关的任务置为已完成
        List<TPoWorkshopPitTemperatureTask> hisTaskList = workshopPitTemperatureTaskMapper.getPitTemperatureTask(insertTemperatureTaskDTO.getCenterId(), insertTemperatureTaskDTO.getLocationId(), insertTemperatureTaskDTO.getPitId());
        if (hisTaskList != null && !hisTaskList.isEmpty()) {
            for (TPoWorkshopPitTemperatureTask hisTask : hisTaskList) {
                hisTask.setTaskStatus(2);
            }
            pitTemperatureTaskRepository.saveAll(hisTaskList);
        }

        return workshopPitTemperatureTaskMapper.insertTemperatureTask(
                new WorkshopPitTemperatureTaskDTO(
                        null,
                        insertTemperatureTaskDTO.getCenterId(),
                        insertTemperatureTaskDTO.getLocationId(),
                        insertTemperatureTaskDTO.getPitId(),
                        insertTemperatureTaskDTO.getVinasseId(),
                        fermentativeMaterialQuantity,
                        crushedGrainsQuantity,
                        ricehullQuantity,
                        waterProportioningQuantity,
                        quQuantity,
                        insertTemperatureTaskDTO.getSealConfirmDate(),
                        fermentationDays,
                        0,
                        insertTemperatureTaskDTO.getCrewId(),
                        insertTemperatureTaskDTO.getGroundTemperature(),
                        insertTemperatureTaskDTO.getInPitTemperature(),
                        null,
                        null,
                        null,
                        insertTemperatureTaskDTO.getPitOrderCode(),
                        insertTemperatureTaskDTO.getLayer(),
                        1
                ));
    }

    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    @Override
    public ResultVO updateTemperatureTask(WorkshopPitTemperatureTaskDTO temperatureTaskDTO) {
        /*long day = (new Date().getTime() - temperatureTaskDTO.getSealConfirmDate().getTime()) / (24 * 60 * 60 * 1000);

        temperatureTaskDTO.setToTopDays((int) day);
        temperatureTaskDTO.setRisesTemperature(temperatureTaskDTO.getTopTemperature() - temperatureTaskDTO.getInPitTemperature());*/

        /*if (workshopPitTemperatureTaskMapper.updateTemperatureTask(temperatureTaskDTO)) {
            // 新增明细
                // 获取最小记录时间计算记录天数
                Date minLogTimeAndMaxId = workshopPitTemperatureTaskMapper.getMinLogTimeAndMaxId(temperatureTaskDTO.getId());
                long logDay = (new Date().getTime() - minLogTimeAndMaxId.getTime()) / (24 * 60 * 60 * 1000);
                workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                        temperatureTaskDTO.getId(),
                        new Date(),
                        (int) logDay
                ));
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }*/
        return ResultVO.error("修改失败");
    }

    @Override
    public List<WorkshopPitTemperatureTaskVO> getAllTemperatureTask() {
        return workshopPitTemperatureTaskMapper.getAllTemperatureTask();
    }

    @Override
    public PageVO<WorkshopPitTemperatureTaskVO> getTemperatureTaskByPage(WorkshopPitTemperatureTaskPageDTO temperatureTaskPageDTO) {
        temperatureTaskPageDTO.setPage((temperatureTaskPageDTO.getPage() - 1) * temperatureTaskPageDTO.getPageSize());
        PageVO<WorkshopPitTemperatureTaskVO> pageVO = new PageVO<>();

        List<WorkshopPitTemperatureTaskVO> temperatureTaskByPage = workshopPitTemperatureTaskMapper.getTemperatureTaskByPage(temperatureTaskPageDTO);

        //查询视图的sql换成下面这个sql来查
        if(CollectionUtils.isNotEmpty(temperatureTaskByPage)){
            List<String> pitOrderCodeList = temperatureTaskByPage.stream().map(WorkshopPitTemperatureTaskVO::getPitOrderCode).collect(Collectors.toList());
            List<VPotTaskAmountDTO> taskAmountDTOList= workshopPitTemperatureTaskMapper.selectVPotTaskAmount(pitOrderCodeList);
            if(CollectionUtils.isNotEmpty(taskAmountDTOList)){

                temperatureTaskByPage.forEach(v->{
                    List<VPotTaskAmountDTO> collect = taskAmountDTOList.stream().filter(item -> item.getPitOrderCode().equals(v.getPitOrderCode())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect)){
                        VPotTaskAmountDTO vPotTaskAmountDTO = collect.get(0);
                        v.setRicehullQuantity(vPotTaskAmountDTO.getRicehullQuantity());
                        v.setWaterProportioningQuantity(vPotTaskAmountDTO.getWaterProportioningQuantity());
                        v.setQuQuantity(vPotTaskAmountDTO.getQuQuantity());
                    }
                });
            }

            List<Integer> idList = temperatureTaskByPage.stream().map(WorkshopPitTemperatureTaskVO::getId).collect(Collectors.toList());

            List<WorkshopPitTemperatureTaskItemsVO> itemsByTaskIdList = workshopPitTemperatureTaskMapper.getItemsByTaskIdList(idList);

            for (WorkshopPitTemperatureTaskVO workshopPitTemperatureTaskVO : temperatureTaskByPage) {
                if(CollectionUtils.isNotEmpty(itemsByTaskIdList)){
                    List<WorkshopPitTemperatureTaskItemsVO> collect = itemsByTaskIdList.stream().filter(item -> item.getPitTemperatureTaskId() == workshopPitTemperatureTaskVO.getId().intValue()).collect(Collectors.toList());
                    workshopPitTemperatureTaskVO.setItemsVOS(CollectionUtils.isEmpty(collect)?new ArrayList<>():collect);
                }else {
                    workshopPitTemperatureTaskVO.setItemsVOS(new ArrayList<>());
                }

                //升至速度保留两位小数
                if (workshopPitTemperatureTaskVO.getToTopSpeedTop() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedTop(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedTop(), 2))));
                }
                if (workshopPitTemperatureTaskVO.getToTopSpeedMiddle() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedMiddle(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedMiddle(), 2))));
                }
                if (workshopPitTemperatureTaskVO.getToTopSpeedBottom() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedBottom(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedBottom(), 2))));
                }
            }
        }





        pageVO.setCount(workshopPitTemperatureTaskMapper.getCount(temperatureTaskPageDTO));
        pageVO.setData(temperatureTaskByPage);
        return pageVO;
    }
    @Override
    public List<TemperatureBoardVO> getTemperatureBoard(String locationCode) {
        final List<TemperatureBoardVO> board = workshopPitTemperatureTaskMapper.getTemperatureBoard(locationCode);
        if (CollectionUtils.isNotEmpty(board)) {
            board.forEach(TemperatureBoardVO::initMinAndMax);
        }
        return board;
    }






    /**
     * 修改明细的（2022.7.21 弃用，变成使用下面那个方法）
     *
     * @param itemsDTOs
     * @return
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    public ResultVO updateTaskItem2(List<WorkshopPitTemperatureTaskItemsDTO> itemsDTOs) {
        int i = 0;
        for (WorkshopPitTemperatureTaskItemsDTO itemsDTO : itemsDTOs) {

            WorkshopPitTemperatureTaskVO taskVO = workshopPitTemperatureTaskMapper.getTaskById(itemsDTO.getPitTemperatureTaskId());
            if (itemsDTO.getPitTemperature() != null) {
                // 先判断修改的记录是否已经存在温度，如果存在温度，则只对温度进行修改
                WorkshopPitTemperatureTaskItemsVO itemsVO = workshopPitTemperatureTaskMapper.getItemTem(itemsDTO.getId());

                if (itemsVO.getPitTemperature() != null || itemsDTO.getTaskStatus() == 2) {
                    // 第一次设置温度并且把状态修改为2
                    itemsVO.setPitTemperature(itemsVO.getPitTemperature() == null ? 0 : itemsVO.getPitTemperature());

                    // 判断即将修改的温度或者是被修改的温度是否为最大温度
                    List<WorkshopPitTemperatureTaskItemsVO> topTemperature = workshopPitTemperatureTaskMapper.getTopTemperature(itemsDTO.getPitTemperatureTaskId());
                    if (itemsDTO.getPitTemperature() > topTemperature.get(0).getPitTemperature() ||
                            itemsVO.getPitTemperature() >= topTemperature.get(0).getPitTemperature()) {
                        // 修改的是当前的最大值
                        if (itemsDTO.getPitTemperature() > itemsVO.getPitTemperature()) {
                            // 说明当前最大值为修改的值，反正当前最大值为查询到的值
                            // 如果是前者，则直接修改升温任务明细跟升温任务中的温度就可以
                            workshopPitTemperatureTaskMapper.updateTemperatureItem(itemsDTO.getPitTemperature(), itemsDTO.getCrewId(), itemsDTO.getId());
                            workshopPitTemperatureTaskMapper.updateTaskTopTem(
                                    itemsDTO.getPitTemperature(),
                                    taskVO.getInPitTemperature(),
                                    itemsDTO.getPitTemperatureTaskId());
                        } else {
                            // 说明当前最大值为 itemsVO.getPitTemperature()，并且我们现在需要将这个最大值改掉
                            // 先获取第二大的温度
                            Float secTemperature = workshopPitTemperatureTaskMapper.getSecTemperature(itemsDTO.getPitTemperatureTaskId());
                            if (secTemperature == null || itemsDTO.getPitTemperature() > secTemperature) {
                                // 如果第二大温度为空则当前修改的温度为最大温度，或者是第二大温度小于修改的温度
                                workshopPitTemperatureTaskMapper.updateTemperatureItem(itemsDTO.getPitTemperature(), itemsDTO.getCrewId(), itemsDTO.getId());
                                workshopPitTemperatureTaskMapper.updateTaskTopTem(
                                        itemsDTO.getPitTemperature(),
                                        taskVO.getInPitTemperature(),
                                        itemsDTO.getPitTemperatureTaskId());
                            } else {
                                // 需要将顶温修改为第二大温度（还得计算升值顶温天数）
                                // 获取第二大顶温列表
                                List<WorkshopPitTemperatureTaskItemsVO> itemsVOS =
                                        workshopPitTemperatureTaskMapper.getSecTemList(itemsDTO.getPitTemperatureTaskId(), secTemperature);

                                // 如果有多个最大温度取记录日期最小的那个
                                WorkshopPitTemperatureTaskItemsVO taskItem = itemsVOS.get(0);
                                long minDate = itemsVOS.get(0).getLogDate().getTime();
                                if (itemsVOS.size() > 1) {
                                    for (WorkshopPitTemperatureTaskItemsVO item : itemsVOS) {
                                        if (item.getLogDate().getTime() < minDate) {
                                            taskItem = item;
                                            minDate = item.getLogDate().getTime();
                                        }
                                    }
                                }
                                // 修改升温任务的记录天数、顶温度、升温幅度和升至顶温天数
                                taskVO.setInPitTemperature(taskVO.getInPitTemperature() == null ? 0 : taskVO.getInPitTemperature());
                                float risesTemperature = secTemperature - taskVO.getInPitTemperature(); // 升温幅度
                                int toTopDays = DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), taskItem.getLogDate()); // 升值顶温所需的最小天数，具体看 sql
                                workshopPitTemperatureTaskMapper.updateTemperatureTask(
                                        null,
                                        secTemperature,
                                        risesTemperature,
                                        toTopDays,
                                        itemsDTO.getPitTemperatureTaskId()
                                );

                                workshopPitTemperatureTaskMapper.updateTemperatureItem(itemsDTO.getPitTemperature(), itemsDTO.getCrewId(), itemsDTO.getId());
                            }
                        }
                    } else {
                        // 修改的值不是最大值，直接进行修改
                        workshopPitTemperatureTaskMapper.updateTemperatureItem(itemsDTO.getPitTemperature(), itemsDTO.getCrewId(), itemsDTO.getId());
                    }
                } else if (itemsDTO.getTaskStatus() != 2) {
                    workshopPitTemperatureTaskMapper.updateTemperatureItem(itemsDTO.getPitTemperature(), itemsDTO.getCrewId(), itemsDTO.getId());
                    // 修改了温度判断是否要将升温状态改为已完成，没有要改为已完成的话新增一条明细
                    itemsDTO.setLogDate(DateUtil.datePlus(1, itemsDTO.getLogDate()));
                    workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                            itemsDTO.getPitTemperatureTaskId(),
                            itemsDTO.getLogDate(),
                            itemsDTO.getLogDays() + 1
                    ));

                    // 修改大记录的记录天数
                    workshopPitTemperatureTaskMapper.updateLogDays(itemsDTO.getLogDays() + 1, itemsDTO.getPitTemperatureTaskId());

                    // 对应的升温任务的顶温度
                    List<WorkshopPitTemperatureTaskItemsVO> taskItems = workshopPitTemperatureTaskMapper.getTopTemperature(itemsDTO.getPitTemperatureTaskId());
                    // 如果有多个最大温度取记录日期最小的那个
                    WorkshopPitTemperatureTaskItemsVO taskItem = taskItems.get(0);
                    long minDate = taskItems.get(0).getLogDate().getTime();
                    if (taskItems.size() > 1) {
                        for (WorkshopPitTemperatureTaskItemsVO item : taskItems) {
                            if (item.getLogDate().getTime() < minDate) {
                                taskItem = item;
                                minDate = item.getLogDate().getTime();
                            }
                        }
                    }
                    // 修改升温任务的记录天数、顶温度、升温幅度和升至顶温天数
                    float topTemperature = taskItem.getPitTemperature(); // 顶温
                    float risesTemperature = taskVO.getInPitTemperature() == null ? topTemperature : topTemperature - taskVO.getInPitTemperature(); // 升温幅度
                    int toTopDays = DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), taskItem.getLogDate()); // 升值顶温所需的最小天数，具体看 sql
                    workshopPitTemperatureTaskMapper.updateTemperatureTask(
                            itemsDTO.getLogDays(),
                            topTemperature,
                            risesTemperature,
                            toTopDays,
                            itemsDTO.getPitTemperatureTaskId()
                    );
                }
            }

            // 判断升温任务的状态
            if (workshopPitTemperatureTaskMapper.getTemperatureTaskStatus(itemsDTO.getPitTemperatureTaskId()) != 2) {
                if (itemsDTO.getTaskStatus() != null) {
                    workshopPitTemperatureTaskMapper.updateTaskStatus(itemsDTO.getTaskStatus(), itemsDTO.getPitTemperatureTaskId());
                    if (itemsDTO.getTaskStatus() == 2) {
                        // 升温任务完成，记录发酵天数
                        workshopPitTemperatureTaskMapper.updateFermentationDays(itemsDTO.getPitTemperatureTaskId());
                        // 填写温度，并对比当前最大温度
                        // 获取最大温度
                        Float maxTem = workshopPitTemperatureTaskMapper.getMaxTem(itemsDTO.getPitTemperatureTaskId());
                        if (maxTem == null || maxTem < itemsDTO.getPitTemperature()) {
                            // 修改升温任务的记录天数、顶温度、升温幅度和升至顶温天数
                            workshopPitTemperatureTaskMapper.updateTemperatureTask(
                                    itemsDTO.getLogDays(),
                                    itemsDTO.getPitTemperature(),
                                    itemsDTO.getPitTemperature() - taskVO.getInPitTemperature(),
                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                    itemsDTO.getPitTemperatureTaskId()
                            );
                        }
                        // 修改温度
                        workshopPitTemperatureTaskMapper.updateTemp(itemsDTO.getPitTemperature(), itemsDTO.getId());
                    }
                }
            } else if (itemsDTO.getTaskStatus() != 2) {
                // 说明要将状态从已完成改为执行
                workshopPitTemperatureTaskMapper.updateTaskStatus(1, itemsDTO.getPitTemperatureTaskId());
                itemsDTO.setLogDate(DateUtil.datePlus(1, itemsDTO.getLogDate()));
                workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                        itemsDTO.getPitTemperatureTaskId(),
                        itemsDTO.getLogDate(),
                        itemsDTO.getLogDays() + 1
                ));
            }
            i++;
            if (i >= itemsDTOs.size()) {
                return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
            }
        }
        return ResultVO.error("修改明细错误");
    }

    /**
     * 修改明细的（2022.7.21 启用，之前是用的上面那个方法）
     * (这里写的很混乱。。。后面维护的话可以重新写过这个业务，把这段代码删了)
     *
     * @param itemsDTOs
     * @return
     */
    @Override
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    public ResultVO updateTaskItem(List<WorkshopPitTemperatureTaskItemsDTO> itemsDTOs) {
        for (WorkshopPitTemperatureTaskItemsDTO itemsDTO : itemsDTOs) {
            if (itemsDTO.getTaskStatus() == null) {
                return ResultVO.error("窖池升温任务状态不能为空");
            }
            Optional<TPoWorkshopPitTemperatureTaskItems> temperatureTaskItemsOptional = pitTemperatureTaskItemsRepository.findById(itemsDTO.getId());
            if (!temperatureTaskItemsOptional.isPresent()) {
                return ResultVO.error("未找到窖池升温任务明细");
            }
            TPoWorkshopPitTemperatureTaskItems temperatureTaskItems = temperatureTaskItemsOptional.get();

            Optional<TPoWorkshopPitTemperatureTask> temperatureTaskOptional = pitTemperatureTaskRepository.findById(temperatureTaskItems.getPitTemperatureTaskId());
            if (!temperatureTaskOptional.isPresent()) {
                return ResultVO.error("未找到窖池升温任务");
            }
            TPoWorkshopPitTemperatureTask temperatureTask = temperatureTaskOptional.get();

            if (temperatureTask.getTaskStatus().equals(2)) {
                return ResultVO.error("窖池升温任务已完成，无法修改");
            }

            //修改明细
            temperatureTaskItems.setPitTemperatureTop(itemsDTO.getPitTemperatureTop());
            temperatureTaskItems.setPitTemperatureMiddle(itemsDTO.getPitTemperatureMiddle());
            temperatureTaskItems.setPitTemperatureBottom(itemsDTO.getPitTemperatureBottom());
            temperatureTaskItems.setUpdateTime(new Date());
            temperatureTaskItems.setUserName(itemsDTO.getUserName());
            pitTemperatureTaskItemsRepository.save(temperatureTaskItems);

            //计算数据
            temperatureTask = temperatureCalculation(temperatureTask);
            temperatureTask.setTaskStatus(itemsDTO.getTaskStatus());
            pitTemperatureTaskRepository.save(temperatureTask);
        }
        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
    }

    /**
     * 修改明细的（2022.7.21 启用，之前是用的上面那个方法）
     * (这里写的很混乱。。。后面维护的话可以重新写过这个业务，把这段代码删了)
     *
     * @param itemsDTOs
     * @return
     */
    @Transactional(isolation = Isolation.DEFAULT, rollbackFor = Exception.class)
    public ResultVO updateTaskItem_old(List<WorkshopPitTemperatureTaskItemsDTO> itemsDTOs) throws ParseException {
        int i = itemsDTOs.size();
        for (WorkshopPitTemperatureTaskItemsDTO itemsDTO : itemsDTOs) {

            if (itemsDTO.getLogDate().getTime() > DateUtil.date(DateUtil.currentDate()).getTime()) {
                throw new RuntimeException("只能填写<=当天日期之前的记录，请勿提前填写！");
            }

            i--;
            if (itemsDTO.getTaskStatus() == null) {
                return ResultVO.error("窖池升温任务状态不能为空");
            }
            /* 1.根据明细查找窖池升温任务 */
            /* 先判断传过来的明细是否存在窖池升温任务的id，跟升温明细id */
            if (itemsDTO.getPitTemperatureTaskId() == null || itemsDTO.getId() == null) {
                return ResultVO.error("窖池升温明细中缺失关联的窖池升温任务id或者是缺失升温明细id");
            }
            /* 获取关联的升温任务 */
            WorkshopPitTemperatureTaskVO taskVO = workshopPitTemperatureTaskMapper.getTaskById(itemsDTO.getPitTemperatureTaskId());
            /* 获取关联升温任务明细中的第二温度 */
            Float secTemperature = workshopPitTemperatureTaskMapper.getSecTemperature(itemsDTO.getPitTemperatureTaskId());
            /* 获取当前数据库中的明细数据 */
            //WorkshopPitTemperatureTaskItemsVO itemTem = workshopPitTemperatureTaskMapper.getItemTem(itemsDTO.getId());

            if (taskVO == null) {
                return ResultVO.error("修改的升温任务在数据库中不存在");
            }
            /* 查看当前窖池升温任务状态 */
            /*if (taskVO.getTaskStatus() == 2) {
                //return ResultVO.error("修改的升温任务不存在；或者修改的升温任务状态为已完成，不能进行修改");
                *//* 说明当前升温状态为已完成状态 *//*
                if (itemsDTO.getTaskStatus() == 2) {
                    *//* 没有进行状态的修改，说明只是想要修改某些值 *//*

                }
            }*/

            /* 2.升温任务要改为已完成状态 */
            if (itemsDTO.getTaskStatus() == 2) {
                if (taskVO.getTopTemperature() == null || itemsDTO.getPitTemperature() > taskVO.getTopTemperature()) {
                    /* 修改升温任务的发酵天数、记录天数、顶温、升温幅度、升至顶温天数 */
                    workshopPitTemperatureTaskMapper.updateTemTask(
                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                            itemsDTO.getLogDays(),
                            itemsDTO.getPitTemperature(),
                            (taskVO.getInPitTemperature() == null ?
                                    null :
                                    itemsDTO.getPitTemperature() - taskVO.getInPitTemperature()
                            ),
                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                            itemsDTO.getTaskStatus(),
                            itemsDTO.getPitTemperatureTaskId()
                    );
                } else {
                    // 这里跟下面有一大段是重复的
                    if (taskVO.getTaskStatus() == 2) {
                        /* 窖池检查任务已经是已完成状态，判断修改的温度是否是当前最大温度 */
                        /* 获取明细中的顶温数据 */
                        List<WorkshopPitTemperatureTaskItemsVO> taskItemsVOS =
                                workshopPitTemperatureTaskMapper.getTopTemperature(itemsDTO.getPitTemperatureTaskId());
                        WorkshopPitTemperatureTaskItemsVO itemsVO = workshopPitTemperatureTaskMapper.getItemTem(itemsDTO.getId());
                        /* 判断当前修改的明细温度是否是修改目前明细顶温列表中的数据 */
                        boolean isTop = false;
                        for (WorkshopPitTemperatureTaskItemsVO taskItemsVO : taskItemsVOS) {
                            if (Objects.equals(taskItemsVO.getId(), itemsVO.getId())) {
                                isTop = true;
                                break;
                            }
                        }

                        /* 如果修改的是当前明细中顶温 */
                        if (isTop) {
                            if (taskItemsVOS.size() > 1) {
                                /* 存在多个顶温 */
                                for (WorkshopPitTemperatureTaskItemsVO taskItemsVO : taskItemsVOS) {
                                    if (!Objects.equals(taskItemsVO.getId(), itemsVO.getId())) {
                                        workshopPitTemperatureTaskMapper.updateTemTask(
                                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                                null,
                                                null,
                                                null,
                                                (Objects.equals(taskItemsVO.getPitTemperature(), itemsDTO.getPitTemperature()) ?
                                                        DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()) :
                                                        DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), taskItemsVO.getLogDate())),
                                                itemsDTO.getTaskStatus(),
                                                itemsDTO.getPitTemperatureTaskId()
                                        );
                                        break;
                                    }
                                }
                            }
                            /* 明细中只存在一个顶温 */
                            else {
                                /* 获取第二大温度列表 */
                                List<WorkshopPitTemperatureTaskItemsVO> secList =
                                        workshopPitTemperatureTaskMapper.getSecTemList(itemsDTO.getPitTemperatureTaskId(), secTemperature);
                                if (secList.size() == 0 ||
                                        Objects.equals(taskVO.getTopTemperature(), itemsDTO.getPitTemperature()) ||
                                        itemsDTO.getPitTemperature() >= secList.get(0).getPitTemperature()) {
                                    /* 说明不存在第二大温度 */
                                    workshopPitTemperatureTaskMapper.updateTemTask(
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                            null,
                                            itemsDTO.getPitTemperature(),
                                            (taskVO.getInPitTemperature() == null ?
                                                    null :
                                                    itemsDTO.getPitTemperature() - taskVO.getInPitTemperature()
                                            ),
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                            itemsDTO.getTaskStatus(),
                                            itemsDTO.getPitTemperatureTaskId()
                                    );
                                } else {
                                    /* 存在第二大温度,获取 id 最小的第二大温度 */
                                    workshopPitTemperatureTaskMapper.updateTemTask(
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                            null,
                                            secList.get(0).getPitTemperature(),
                                            (taskVO.getInPitTemperature() == null ?
                                                    null :
                                                    secList.get(0).getPitTemperature() - taskVO.getInPitTemperature()
                                            ),
                                            (Objects.equals(secList.get(0).getPitTemperature(), itemsDTO.getPitTemperature()) ?
                                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()) :
                                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), secList.get(0).getLogDate())),
                                            itemsDTO.getTaskStatus(),
                                            itemsDTO.getPitTemperatureTaskId()
                                    );
                                }
                            }
                        }
                        /* 修改的不是当前顶温 */
                        else {
                            workshopPitTemperatureTaskMapper.updateTemTask(
                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                    null,
                                    null,
                                    null,
                                    null,
                                    itemsDTO.getTaskStatus(),
                                    itemsDTO.getPitTemperatureTaskId()
                            );
                        }
                    } else {
                        workshopPitTemperatureTaskMapper.updateTemTask(
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                itemsDTO.getLogDays(),
                                null,
                                null,
                                null,
                                itemsDTO.getTaskStatus(),
                                itemsDTO.getPitTemperatureTaskId()
                        );
                    }
                }
                /* 修改明细 */
                if (workshopPitTemperatureTaskMapper.updateTemperatureItem(
                        itemsDTO.getPitTemperature(),
                        itemsDTO.getCrewId(),
                        itemsDTO.getId())) {
                    if (i == 0) {
                        return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                    }
                    continue;
                }
                return ResultVO.error("修改明细错误");
            }

            /* 3.升温任务为未完成状态 */
            if (itemsDTO.getTaskStatus() != 2) {
                /* 判断当前修改的明细在数据库中是否存在温度 */
                WorkshopPitTemperatureTaskItemsVO itemsVO = workshopPitTemperatureTaskMapper.getItemTem(itemsDTO.getId());
                if (itemsVO == null) {
                    return ResultVO.error("您要修改的升温明细在数据库中找不到");
                }
                if (itemsVO.getPitTemperature() == null) {
                    if (taskVO.getTopTemperature() == null || itemsDTO.getPitTemperature() > taskVO.getTopTemperature()) {
                        /* 修改升温任务的发酵天数、记录天数、顶温、升温幅度、升至顶温天数 */
                        workshopPitTemperatureTaskMapper.updateTemTask(
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                itemsDTO.getLogDays() + 1,
                                itemsDTO.getPitTemperature(),
                                (taskVO.getInPitTemperature() == null ?
                                        null :
                                        itemsDTO.getPitTemperature() - taskVO.getInPitTemperature()
                                ),
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                itemsDTO.getTaskStatus(),
                                itemsDTO.getPitTemperatureTaskId()
                        );
                    } else {
                        workshopPitTemperatureTaskMapper.updateTemTask(
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                itemsDTO.getLogDays() + 1,
                                null,
                                null,
                                null,
                                itemsDTO.getTaskStatus(),
                                itemsDTO.getPitTemperatureTaskId()
                        );
                    }

                    /* 修改明细 */
                    if (workshopPitTemperatureTaskMapper.updateTemperatureItem(
                            itemsDTO.getPitTemperature(),
                            itemsDTO.getCrewId(),
                            itemsDTO.getId())) {
                        /* 成功后自动生成下一天的数据 */
                        itemsDTO.setLogDate(DateUtil.datePlus(1, itemsDTO.getLogDate()));
                        workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                                itemsDTO.getPitTemperatureTaskId(),
                                itemsDTO.getLogDate(),
                                itemsDTO.getLogDays() + 1
                        ));
                        if (i == 0) {
                            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                        }
                        continue;
                    }
                    return ResultVO.error("修改明细错误");
                }
                /* 修改的是之前的明细，不是修改的最新一条 */
                else {
                    /* 修改的温度大于顶温，更新顶温 */
                    if (itemsDTO.getPitTemperature() > taskVO.getTopTemperature()) {
                        /* 修改升温任务的发酵天数、记录天数、顶温、升温幅度、升至顶温天数 */
                        workshopPitTemperatureTaskMapper.updateTemTask(
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                null,
                                itemsDTO.getPitTemperature(),
                                (taskVO.getInPitTemperature() == null ?
                                        null :
                                        itemsDTO.getPitTemperature() - taskVO.getInPitTemperature()
                                ),
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                itemsDTO.getTaskStatus(),
                                itemsDTO.getPitTemperatureTaskId()
                        );
                    }
                    /* 修改的温度等于顶温，更新升值顶温天数 */
                    if (Objects.equals(itemsDTO.getPitTemperature(), taskVO.getTopTemperature()) &&
                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()) < taskVO.getToTopDays()) {
                        /* 修改升温任务的发酵天数、记录天数、顶温、升温幅度、升至顶温天数 */
                        workshopPitTemperatureTaskMapper.updateTemTask(
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                null,
                                null,
                                null,
                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                itemsDTO.getTaskStatus(),
                                itemsDTO.getPitTemperatureTaskId()
                        );
                    }
                    /* 修改的温度小于等于顶温 */
                    else {
                        /* 获取明细中的顶温数据 */
                        List<WorkshopPitTemperatureTaskItemsVO> taskItemsVOS =
                                workshopPitTemperatureTaskMapper.getTopTemperature(itemsDTO.getPitTemperatureTaskId());
                        /* 判断当前修改的明细温度是否是修改目前明细顶温列表中的数据 */
                        boolean isTop = false;
                        for (WorkshopPitTemperatureTaskItemsVO taskItemsVO : taskItemsVOS) {
                            if (Objects.equals(taskItemsVO.getId(), itemsVO.getId())) {
                                isTop = true;
                                break;
                            }
                        }

                        /* 如果修改的是当前明细中顶温 */
                        if (isTop) {
                            if (taskItemsVOS.size() > 1) {
                                /* 存在多个顶温 */
                                for (WorkshopPitTemperatureTaskItemsVO taskItemsVO : taskItemsVOS) {
                                    if (!Objects.equals(taskItemsVO.getId(), itemsVO.getId())) {
                                        workshopPitTemperatureTaskMapper.updateTemTask(
                                                DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                                null,
                                                null,
                                                null,
                                                (Objects.equals(taskItemsVO.getPitTemperature(), itemsDTO.getPitTemperature()) ?
                                                        DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()) :
                                                        DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), taskItemsVO.getLogDate())),
                                                itemsDTO.getTaskStatus(),
                                                itemsDTO.getPitTemperatureTaskId()
                                        );
                                        break;
                                    }
                                }
                            }
                            /* 明细中只存在一个顶温 */
                            else {
                                /* 获取第二大温度列表 */
                                List<WorkshopPitTemperatureTaskItemsVO> secList =
                                        workshopPitTemperatureTaskMapper.getSecTemList(itemsDTO.getPitTemperatureTaskId(), secTemperature);
                                if (secList.size() == 0 ||
                                        Objects.equals(taskVO.getTopTemperature(), itemsDTO.getPitTemperature()) ||
                                        itemsDTO.getPitTemperature() >= secList.get(0).getPitTemperature()) {
                                    /* 说明不存在第二大温度 */
                                    workshopPitTemperatureTaskMapper.updateTemTask(
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                            null,
                                            itemsDTO.getPitTemperature(),
                                            (taskVO.getInPitTemperature() == null ?
                                                    null :
                                                    itemsDTO.getPitTemperature() - taskVO.getInPitTemperature()
                                            ),
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()),
                                            itemsDTO.getTaskStatus(),
                                            itemsDTO.getPitTemperatureTaskId()
                                    );
                                } else {
                                    /* 存在第二大温度,获取 id 最小的第二大温度 */
                                    workshopPitTemperatureTaskMapper.updateTemTask(
                                            DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                            null,
                                            secList.get(0).getPitTemperature(),
                                            (taskVO.getInPitTemperature() == null ?
                                                    null :
                                                    secList.get(0).getPitTemperature() - taskVO.getInPitTemperature()
                                            ),
                                            (Objects.equals(secList.get(0).getPitTemperature(), itemsDTO.getPitTemperature()) ?
                                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), itemsDTO.getLogDate()) :
                                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), secList.get(0).getLogDate())),
                                            itemsDTO.getTaskStatus(),
                                            itemsDTO.getPitTemperatureTaskId()
                                    );
                                }
                            }
                        }
                        /* 修改的不是当前顶温 */
                        else {
                            workshopPitTemperatureTaskMapper.updateTemTask(
                                    DateUtil.subtractTwoDates(taskVO.getSealConfirmDate(), new Date()),
                                    null,
                                    null,
                                    null,
                                    null,
                                    itemsDTO.getTaskStatus(),
                                    itemsDTO.getPitTemperatureTaskId()
                            );
                        }
                    }
                    /* 修改明细 */
                    if (workshopPitTemperatureTaskMapper.updateTemperatureItem(
                            itemsDTO.getPitTemperature(),
                            itemsDTO.getCrewId(),
                            itemsDTO.getId())) {
                        if (taskVO.getTaskStatus() == 2) {
                            workshopPitTemperatureTaskMapper.updateTemTask(
                                    null,
                                    itemsDTO.getLogDays() + 1,
                                    null,
                                    null,
                                    null,
                                    null,
                                    itemsDTO.getPitTemperatureTaskId()
                            );
                            /* 成功后自动生成下一天的数据 */
                            itemsDTO.setLogDate(DateUtil.datePlus(1, itemsDTO.getLogDate()));
                            workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                                    itemsDTO.getPitTemperatureTaskId(),
                                    itemsDTO.getLogDate(),
                                    itemsDTO.getLogDays() + 1
                            ));
                        }
                        if (i == 0) {
                            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
                        }
                        continue;
                    }
                    return ResultVO.error("修改明细错误");
                }
            }
        }
        return ResultVO.error("修改明细错误");
    }

    @Override
    public List<TaskItemsByIds> getTaskItemsByTaskIds(GetTaskItemsByIds ids) {
        List<TaskItemsByIds> itemsVOS = new ArrayList<>();
        for (Integer id : ids.getIds()) {
            TaskItemsByIds itemsVO = new TaskItemsByIds();
            itemsVO.setTaskId(id);
            itemsVO.setItemsVOS(workshopPitTemperatureTaskMapper.getItemsByTaskId(id));
            itemsVOS.add(itemsVO);
        }
        return itemsVOS;
    }

    /**
     * 获取每一个升温任务的升温明细（最新一条）
     *
     * @return
     */
    @Override
    public List<WorkshopPitTemperatureTaskItemsVO> getTaskItems(int[] ids) {
        if (ids.length <= 0) {
            return null;
        }
        List<WorkshopPitTemperatureTaskItemsVO> taskItems = workshopPitTemperatureTaskMapper.getTaskItems(ids);
        for (WorkshopPitTemperatureTaskItemsVO taskItem : taskItems) {
            // 查询上一次温度
            taskItem.setPitTemperature(workshopPitTemperatureTaskMapper.LastTemp(taskItem.getPitTemperatureTaskId()));
        }
        return taskItems;
    }

    /**
     * 窖池升温任务报表数据
     */
    @Override
    public List<TemTaskReportVO> TemTaskReportData(TemTaskReportDTO temTaskReportDTO) {
        if (temTaskReportDTO.getCenterId() == null) {
            throw new RuntimeException("中心不能为空");
        }
        return workshopPitTemperatureTaskMapper.TemTaskReportData(temTaskReportDTO);
    }

    /**
     * 窖池升温任务报表
     */
    @Override
    public ExcelExportDto TemTaskReport(TemTaskReportDTO temTaskReportDTO) throws IOException {
        if (temTaskReportDTO.getCenterId() == null) {
            throw new RuntimeException("中心不能为空");
        }
        // 头部
        List<List<String>> head = head(temTaskReportDTO);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        String sheetName = temTaskReportDTO.getCenterId() + "中心";

        List<TemTaskReportVO> temTaskReportVOS = workshopPitTemperatureTaskMapper.TemTaskReportData(temTaskReportDTO);

        // 合并单元格信息
        List<MergeCellModel> mergeCellList = new ArrayList<>();
        if (temTaskReportVOS.size() > 0) {
            int startRowIndex = 2;
            int endRowIndex = 2;
            int startLocationRowIndex = 2;
            String center = temTaskReportVOS.get(0).getCenterCode();
            for (int i = 1; i < temTaskReportVOS.size(); i++) {
                TemTaskReportVO temTaskReportVO = temTaskReportVOS.get(i);
                if (temTaskReportVO.getCenterCode().equals(center)) {
                    endRowIndex++;
                } else {
                    mergeCellList.add(MergeCellInfoInit.createMergeRowCellModel(sheetName, startRowIndex, endRowIndex, 0));
                    startRowIndex = endRowIndex;
                }
                if (i == temTaskReportVOS.size() - 1) {
                    mergeCellList.add(MergeCellInfoInit.createMergeRowCellModel(sheetName, startRowIndex, endRowIndex, 0));
                }
                // 合并相同车间
                if (i == temTaskReportVOS.size() - 1) {
                    // 最后一个
                    mergeCellList.add(MergeCellInfoInit.createMergeRowCellModel(sheetName, startLocationRowIndex, 2 + i, 1));
                }
                if (!temTaskReportVOS.get(i - 1).getLocationCode().equals(temTaskReportVO.getLocationCode())) {
                    // 前一个中心code跟后一个中心code不相同，合并前面的
                    mergeCellList.add(MergeCellInfoInit.createMergeRowCellModel(sheetName, startLocationRowIndex, 2 + i - 1, 1));
                    startLocationRowIndex = startLocationRowIndex + i;
                }
            }
        }

        // sheet页内容
        List<List<String>> list = new ArrayList<>();
        for (TemTaskReportVO temTaskReportVO : temTaskReportVOS) {
            List<String> contain = new ArrayList<>();
            contain.add(temTaskReportVO.getCenterCode());
            contain.add(temTaskReportVO.getLocationCode());
            contain.add(temTaskReportVO.getPitCode());
            if (temTaskReportVO.getReportVOS().size() < 1) {
                list.add(contain);
                break;
            }

            contain.add(temTaskReportVO.getReportVOS().get(0).getSealConfirmDate() == null ? null :
                    DateUtil.datetimeToDate(temTaskReportVO.getReportVOS().get(0).getSealConfirmDate()));

            contain.add(temTaskReportVO.getReportVOS().get(0).getGroundTemperature() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getGroundTemperature().setScale(1, RoundingMode.HALF_UP).toString());

            contain.add(temTaskReportVO.getReportVOS().get(0).getInPitTemperature() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getInPitTemperature().setScale(1, RoundingMode.HALF_UP).toString());

            contain.add(temTaskReportVO.getReportVOS().get(0).getTopTemperature() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getTopTemperature().setScale(1, RoundingMode.HALF_UP).toString());

            contain.add(temTaskReportVO.getReportVOS().get(0).getInPitTemperature() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getInPitTemperature().setScale(1, RoundingMode.HALF_UP).toString());

            contain.add(temTaskReportVO.getReportVOS().get(0).getRisesTemperature() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getRisesTemperature().setScale(1, RoundingMode.HALF_UP).toString());

            contain.add(temTaskReportVO.getReportVOS().get(0).getToTopDays() == null ? null :
                    temTaskReportVO.getReportVOS().get(0).getToTopDays() + "");

            for (TemTaskDetailReportVO reportVO : temTaskReportVO.getReportVOS()) {
                contain.add(reportVO.getPitTemperature() == null ? null :
                        reportVO.getPitTemperature().setScale(1, RoundingMode.HALF_UP).toString());
            }

            list.add(contain);
        }

        ExcelWriter excelWriter = EasyExcel.write(outputStream)
                .head(head)
                .registerWriteHandler(new CustomMergeCellHandler(mergeCellList))
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .registerWriteHandler(new TemTaskStyleCellHandler())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(sheetName).build();
        excelWriter.write(list, writeSheet);
        excelWriter.finish();
        outputStream.close();

        ExcelExportDto exportDto = new ExcelExportDto();
        exportDto.setFileName(sheetName + "升温统计（各酿酒中心）.xlsx");
        exportDto.setBody(outputStream.toByteArray());
        return exportDto;
    }

    /**
     * 查询窖池任务关联明细最大的条数
     *
     * @param temTaskReportDTO
     * @return
     */
    @Override
    public Integer TemTaskMaxNum(TemTaskReportDTO temTaskReportDTO) {
        if (temTaskReportDTO.getCenterId() == null) {
            throw new RuntimeException("中心不能为空");
        }
        return workshopPitTemperatureTaskMapper.TemTaskMaxNum(temTaskReportDTO);
    }

    /**
     * 窖池升温情况报表数据查询(按月统计窖池升温情况)
     *
     * @param temTaskReportDTO
     * @return
     */
    @Override
    public List<TemTaskMonthReportVO> dataQuery(TemTaskReportDTO temTaskReportDTO) {
        if (temTaskReportDTO.getStartTime() == null) {
            throw new RuntimeException("时间不能为空");
        }

        return workshopPitTemperatureTaskMapper.dataQuery(temTaskReportDTO);
    }

    /**
     * 窖池升温情况报表（按月统计窖池升温情况）
     *
     * @return
     */
    @Override
    public ExcelExportDto reportDataByMonth(TemTaskReportDTO temTaskReportDTO) throws IOException {
        if (temTaskReportDTO.getStartTime() == null) {
            throw new RuntimeException("时间不能为空");
        }
        int year;
        int month;
        try {
            Date toDate = DateUtil.toDate(temTaskReportDTO.getStartTime());
            year = cn.hutool.core.date.DateUtil.year(toDate);
            month = cn.hutool.core.date.DateUtil.month(toDate);
        } catch (Exception e) {
            throw new RuntimeException("日期转换错误，请联系管理员检查日期格式是否正确");
        }
        String filename = year + "年" + month + "月窖池升温情况.xlsx";
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        List<TemTaskMonthReportVO> temTaskMonthReportVOS = workshopPitTemperatureTaskMapper.dataQuery(temTaskReportDTO);
        ExcelWriter excelWriter = EasyExcel.write(outputStream, TemTaskMonthReportVO.class)
                .registerWriteHandler(HeadAndContentCellStyle.getCellStyleToStatistical())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(year + "年" + month + "月窖池升温情况").build();
        excelWriter.write(temTaskMonthReportVOS, writeSheet);
        excelWriter.finish();
        outputStream.close();

        ExcelExportDto dto = new ExcelExportDto();
        dto.setFileName(filename);
        dto.setBody(outputStream.toByteArray());
        return dto;
    }

    /**
     * 手动新增
     *
     * @param dto
     * @return
     */
    @Override
    public ResultVO AddManuallyTemp(AddManuallyTempDTO dto) {
        if (dto.getOrderId() == null) {
            return ResultVO.error("窖池订单id不能为空");
        }

        // 校验窖池订单不能重复添加
        Integer nums = workshopPitTemperatureTaskMapper.checkOrder(dto.getOrderId());
        if (nums != null && nums > 0) {
            throw new RuntimeException("窖池订单不能重复进行升温任务");
        }

        // 查询窖池订单信息
        WorkshopPitTemperatureTaskDTO taskDTO = workshopPitTemperatureTaskMapper.getOrder(dto.getOrderId());
        if (taskDTO == null) {
            throw new RuntimeException("不存在窖池订单信息，手动新增失败");
        }
        if (taskDTO.getSealConfirmDate() == null) {
            throw new RuntimeException("封窖确认时间为空，新增窖池升温任务失败");
        }

        // 发酵天数
        int fermentationDays = DateUtil.subtractTwoDates(taskDTO.getSealConfirmDate(), new Date());
        taskDTO.setFermentationDays(fermentationDays);
        taskDTO.setCenterId(dto.getCenterId());
        taskDTO.setLocationId(dto.getLocationId());
        taskDTO.setPitId(dto.getPitId());
        taskDTO.setTaskStatus(0);
        taskDTO.setLogDays(1);

        if (workshopPitTemperatureTaskMapper.insertTemperatureTask(taskDTO)) {
            int maxId = workshopPitTemperatureTaskMapper.getMaxId();
            Boolean isAdd = workshopPitTemperatureTaskMapper.insertTemperatureTaskItem(new WorkshopPitTemperatureTaskItemsDTO(
                    maxId,
                    DateUtil.datePlus(1, taskDTO.getSealConfirmDate()),
                    1
            ));
            if (isAdd) {
                return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
            } else {
                return ResultVO.error("新增失败");
            }
        }
        return ResultVO.error("新增失败");
    }

    /**
     * 窖池升温任务手动新增窖池订单信息查询接口
     *
     * @return
     */
    @Override
    public List<WorkshopPitOrderDTO> OrderInfo(Integer centerId, Integer locationId) {
        return workshopPitTemperatureTaskMapper.OrderInfo(centerId, locationId);
    }

    /**
     * 根据id删除升温任务
     *
     * @param tempId
     * @return
     */
    @Override
    public ResultVO DeleteTemp(Integer tempId) {
        if (workshopPitTemperatureTaskMapper.DeleteTemp(tempId)) {
            workshopPitTemperatureTaskMapper.DeleteTempItem(tempId);
            return ResultVO.success(ResultTipEnum.SUCCESS.getMessage());
        }
        return ResultVO.error("删除升温任务失败");
    }

    /**
     * 接收无线测温数据
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultVO insertPoolTemperature(List<PoolTemperatureDTO> dtos) {
        Map<String, TPoWorkshopPitTemperatureTask> taskMap = new HashMap<String, TPoWorkshopPitTemperatureTask>();//升温任务数据缓存
        boolean isException = false;
        String errorMsgs = "";
        Integer i = 1;
        for (PoolTemperatureDTO dto : dtos) {
            String errorMsg = "";
            try {
                TPoWorkshopPitTemperatureTask pitTemperatureTask = null;
                //获取匹配最新升温任务
                String key = dto.getCenter() + "|" + dto.getLocation() + "|" + dto.getPitCode();
                if (taskMap.containsKey(key)) {
                    //已存在则直接取
                    pitTemperatureTask = taskMap.get(key);
                } else {
                    //不存在则从数据库查询
                    Optional<TPoWorkshopPitTemperatureTask> pitTemperatureTaskOptional = workshopPitTemperatureTaskMapper.getLastPitTemperatureTask(dto.getCenter(), dto.getLocation(), dto.getPitCode());
                    if (pitTemperatureTaskOptional.isPresent()) {
                        pitTemperatureTask = pitTemperatureTaskOptional.get();
                        taskMap.put(key, pitTemperatureTask);//查询到就加入缓存
                    } else {
                        System.out.println("未获取到最新升温任务:" + dto.getCenter() + "|" + dto.getLocation() + "|" + dto.getPitCode());
                        errorMsg = "第" + i + "行数据未获取到最新升温任务";
                    }
                }
                if (pitTemperatureTask != null) {
                    //未记录过入窖温度则先记录
                    if (pitTemperatureTask.getInPitTemperatureTop() == null && pitTemperatureTask.getInPitTemperatureMiddle() == null && pitTemperatureTask.getInPitTemperatureBottom() == null) {
                        pitTemperatureTask.setInPitTemperatureTop(dto.getTop());
                        pitTemperatureTask.setInPitTemperatureMiddle(dto.getMiddle());
                        pitTemperatureTask.setInPitTemperatureBottom(dto.getBottom());
                        pitTemperatureTask.setLogDays(0);//第0天
                    } else {
                        //已记录过则新增明细
                        //按日期判断是否重复
                        Date logDate = Date.from(dto.getDate().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());

                        List<TPoWorkshopPitTemperatureTaskItems> hisTaskItems = pitTemperatureTaskItemsRepository.findByPitTemperatureTaskIdAndLogDate(pitTemperatureTask.getId(), logDate);
                        if (hisTaskItems != null && !hisTaskItems.isEmpty()) {
                            //已存在则更新第一条（正常只会有一条）
                            TPoWorkshopPitTemperatureTaskItems hisTaskItem = hisTaskItems.get(0);

                            hisTaskItem.setPitTemperatureTop(dto.getTop());
                            hisTaskItem.setPitTemperatureMiddle(dto.getMiddle());
                            hisTaskItem.setPitTemperatureBottom(dto.getBottom());
                            hisTaskItem.setUpdateTime(new Date());
                            hisTaskItem.setUserName("无线测温");
                            pitTemperatureTaskItemsRepository.save(hisTaskItem);
                        } else {
                            pitTemperatureTask.setLogDays(pitTemperatureTask.getLogDays() + 1);//天数累加

                            TPoWorkshopPitTemperatureTaskItems newItem = new TPoWorkshopPitTemperatureTaskItems();
                            newItem.setPitTemperatureTaskId(pitTemperatureTask.getId());
                            newItem.setCrewId(pitTemperatureTask.getCrewId());
                            newItem.setLogDate(logDate);
                            newItem.setLogDays(pitTemperatureTask.getLogDays());
                            newItem.setPitTemperatureTop(dto.getTop());
                            newItem.setPitTemperatureMiddle(dto.getMiddle());
                            newItem.setPitTemperatureBottom(dto.getBottom());
                            newItem.setUpdateTime(new Date());
                            newItem.setUserName("无线测温");
                            pitTemperatureTaskItemsRepository.save(newItem);
                        }
                    }
                    pitTemperatureTask = temperatureCalculation(pitTemperatureTask);
                    pitTemperatureTaskRepository.save(pitTemperatureTask);

                    taskMap.put(key, pitTemperatureTask);//刷新缓存
                }
            } catch (Exception e) {

                e.printStackTrace();

                System.out.println(e);

                errorMsg = "第" + i + "行数据处理异常：" + e.getMessage();
                isException = true;
            }
            i++;
            //添加消息
            if (errorMsg != "") {
                errorMsgs += errorMsg + "。";
            }
        }

        //记录接口日志
        LogDto logDto = new LogDto();
        if (errorMsgs != "") {
            logDto.setLogType(2);
            logDto.setLogExceptionMessage(errorMsgs);
        } else {
            logDto.setLogType(1);
        }
        if (dtos != null) {
            try {
                logDto.setControllerName("MonitorController");
                logDto.setLogCaptureTime(new Date());
                logDto.setLogModular("brewage-server");
                logDto.setLogInvocation("无线测温");
                logDto.setLocation("brewage-server/monitor/pool-temperature");
                logDto.setMethodName("无线测温数据接收");
                logDto.setLogParameter(JSONObject.toJSONString(dtos));
                logCaptureClient.logRecord(logDto);
            } catch (Exception e) {
                log.warn("insertPoolTemperature==记录日志失败：" + e.getMessage() + "=>" + JSONUtil.toJsonStr(dtos));
            }
        }

        //添加窖池升温记录数据
        addTaskTemperatureDetail(dtos);

        if (errorMsgs != "") {
            if (isException) {
                //存在异常，则抛出，并触发事务回滚
                throw new BaseKnownException(10000, errorMsgs);
            } else {
                //有消息无异常，则返回成功，但是附带消息
                return new ResultVO(200, errorMsgs);
            }
        }
        return new ResultVO(200, null);
    }

    /**
     * 添加窖池升温记录明细数据
     * @param dtos
     */
    public void addTaskTemperatureDetail(List<PoolTemperatureDTO> dtos){
        log.info("无线测温数据主动推数据，添加窖池升温记录明细数据，参数-----》{}",JSONObject.toJSONString(dtos));
        dtos.forEach(v->{
            TaskTemperatureDetailAddDTO addDTO = new TaskTemperatureDetailAddDTO();
            //根据窖号查询连窖号
            String pitNo = taskSpreadingMapper.selectPitNo(v.getPitCode());
            addDTO.setPitNo(pitNo);
            addDTO.setCenter(v.getCenter());
            addDTO.setLocation(v.getLocation());
            addDTO.setUpperTemp(BigDecimal.valueOf(v.getTop()));
            addDTO.setMiddleTemp(BigDecimal.valueOf(v.getMiddle()));
            addDTO.setBottomTemp(BigDecimal.valueOf(v.getBottom()));
            Date recordDate = Date.from(v.getDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
            addDTO.setRecordDate(recordDate);
            addDTO.setDataSource(TaskBusiness.TASK_TEMPERATURE_CW.getTaskType());
            taskTemperatureDetailService.add(addDTO);
        });

    }

    @Override
    public List<WorkshopPitTemperatureTaskVO> getTemperatureTask(WorkshopPitTemperatureTaskPageDTO temperatureTaskPageDTO) {
        List<WorkshopPitTemperatureTaskVO> temperatureTask = workshopPitTemperatureTaskMapper.getTemperatureTaskByPage(temperatureTaskPageDTO);

        //查询视图的sql换成下面这个sql来查
        if(CollectionUtils.isNotEmpty(temperatureTask)){
            List<String> pitOrderCodeList = temperatureTask.stream().map(WorkshopPitTemperatureTaskVO::getPitOrderCode).collect(Collectors.toList());
            List<VPotTaskAmountDTO> taskAmountDTOList= workshopPitTemperatureTaskMapper.selectVPotTaskAmount(pitOrderCodeList);
            if(CollectionUtils.isNotEmpty(taskAmountDTOList)){

                temperatureTask.forEach(v->{
                    List<VPotTaskAmountDTO> collect = taskAmountDTOList.stream().filter(item -> item.getPitOrderCode().equals(v.getPitOrderCode())).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(collect)){
                        VPotTaskAmountDTO vPotTaskAmountDTO = collect.get(0);
                        v.setRicehullQuantity(vPotTaskAmountDTO.getRicehullQuantity());
                        v.setWaterProportioningQuantity(vPotTaskAmountDTO.getWaterProportioningQuantity());
                        v.setQuQuantity(vPotTaskAmountDTO.getQuQuantity());
                    }
                });
            }

            List<Integer> idList = temperatureTask.stream().map(WorkshopPitTemperatureTaskVO::getId).collect(Collectors.toList());

            List<WorkshopPitTemperatureTaskItemsVO> itemsByTaskIdList = workshopPitTemperatureTaskMapper.getItemsByTaskIdList(idList);

            for (WorkshopPitTemperatureTaskVO workshopPitTemperatureTaskVO : temperatureTask) {
                if(CollectionUtils.isNotEmpty(itemsByTaskIdList)){
                    List<WorkshopPitTemperatureTaskItemsVO> collect = itemsByTaskIdList.stream().filter(item -> item.getPitTemperatureTaskId() == workshopPitTemperatureTaskVO.getId().intValue()).collect(Collectors.toList());
                    workshopPitTemperatureTaskVO.setItemsVOS(CollectionUtils.isEmpty(collect)?new ArrayList<>():collect);
                }else {
                    workshopPitTemperatureTaskVO.setItemsVOS(new ArrayList<>());
                }

                //升至速度保留两位小数
                if (workshopPitTemperatureTaskVO.getToTopSpeedTop() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedTop(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedTop(), 2))));
                }
                if (workshopPitTemperatureTaskVO.getToTopSpeedMiddle() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedMiddle(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedMiddle(), 2))));
                }
                if (workshopPitTemperatureTaskVO.getToTopSpeedBottom() != null) {
                    workshopPitTemperatureTaskVO.setToTopSpeedBottom(Float.parseFloat(String.valueOf(MathUtils.round(workshopPitTemperatureTaskVO.getToTopSpeedBottom(), 2))));
                }
            }
        }
        return temperatureTask;
    }

    /**
     * 计算升温任务数据
     *
     * @param pitTemperatureTask
     * @return
     */
    private TPoWorkshopPitTemperatureTask temperatureCalculation(TPoWorkshopPitTemperatureTask pitTemperatureTask) {
        //查一把，按时间排序
        List<TPoWorkshopPitTemperatureTaskItems> pitTemperatureTaskItems = pitTemperatureTaskItemsRepository.findByPitTemperatureTaskIdOrderByLogDateAsc(pitTemperatureTask.getId());

        //按顺序刷新下记录天数
        if (!pitTemperatureTaskItems.isEmpty()) {
            Integer logDays = 1;//从1开始
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                pitTemperatureTaskItem.setLogDays(logDays);
                logDays++;
            }
            pitTemperatureTaskItemsRepository.saveAll(pitTemperatureTaskItems);
        }

        //解析升温明细计算
        if (pitTemperatureTaskItems.size() >= 20) {//超过20条则触发计算
            //顶温=最高温度
            pitTemperatureTask.setTopTemperatureTop(0F);
            pitTemperatureTask.setTopTemperatureMiddle(0F);
            pitTemperatureTask.setTopTemperatureBottom(0F);
            Optional<TPoWorkshopPitTemperatureTaskItems> itemTopOptional = pitTemperatureTaskItems.stream().filter(s -> s.getPitTemperatureTop() != null).max(Comparator.comparing(TPoWorkshopPitTemperatureTaskItems::getPitTemperatureTop));
            if (itemTopOptional.isPresent()) {
                pitTemperatureTask.setTopTemperatureTop(itemTopOptional.get().getPitTemperatureTop());
            }
            Optional<TPoWorkshopPitTemperatureTaskItems> itemMiddleOptional = pitTemperatureTaskItems.stream().filter(s -> s.getPitTemperatureMiddle() != null).max(Comparator.comparing(TPoWorkshopPitTemperatureTaskItems::getPitTemperatureMiddle));
            if (itemMiddleOptional.isPresent()) {
                pitTemperatureTask.setTopTemperatureMiddle(itemMiddleOptional.get().getPitTemperatureMiddle());
            }
            Optional<TPoWorkshopPitTemperatureTaskItems> itemBottomOptional = pitTemperatureTaskItems.stream().filter(s -> s.getPitTemperatureBottom() != null).max(Comparator.comparing(TPoWorkshopPitTemperatureTaskItems::getPitTemperatureBottom));
            if (itemBottomOptional.isPresent()) {
                pitTemperatureTask.setTopTemperatureBottom(itemBottomOptional.get().getPitTemperatureBottom());
            }

            //前缓期天数=上升至 最高温度所需的天数
            Integer toTopDaysTop = 1;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                //遍历到最高温度前有多少条数据
                if (pitTemperatureTaskItem.getPitTemperatureTop() != null && pitTemperatureTaskItem.getPitTemperatureTop().equals(pitTemperatureTask.getTopTemperatureTop())) {
                    break;
                }
                toTopDaysTop++;
            }
            pitTemperatureTask.setToTopDaysTop(toTopDaysTop);
            Integer toTopDaysMiddle = 1;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                //遍历到最高温度前有多少条数据
                if (pitTemperatureTaskItem.getPitTemperatureMiddle() != null && pitTemperatureTaskItem.getPitTemperatureMiddle().equals(pitTemperatureTask.getTopTemperatureMiddle())) {
                    break;
                }
                toTopDaysMiddle++;
            }
            pitTemperatureTask.setToTopDaysMiddle(toTopDaysMiddle);
            Integer toTopDaysBottom = 1;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                //遍历到最高温度前有多少条数据
                if (pitTemperatureTaskItem.getPitTemperatureBottom() != null && pitTemperatureTaskItem.getPitTemperatureBottom().equals(pitTemperatureTask.getTopTemperatureBottom())) {
                    break;
                }
                toTopDaysBottom++;
            }
            pitTemperatureTask.setToTopDaysBottom(toTopDaysBottom);

            //升温幅度=最高温度-入窖温度
            pitTemperatureTask.setRisesTemperatureTop(pitTemperatureTask.getTopTemperatureTop() - (pitTemperatureTask.getInPitTemperatureTop() == null ? 0F : pitTemperatureTask.getInPitTemperatureTop()));
            pitTemperatureTask.setRisesTemperatureMiddle(pitTemperatureTask.getTopTemperatureMiddle() - (pitTemperatureTask.getInPitTemperatureMiddle() == null ? 0F : pitTemperatureTask.getInPitTemperatureMiddle()));
            pitTemperatureTask.setRisesTemperatureBottom(pitTemperatureTask.getTopTemperatureBottom() - (pitTemperatureTask.getInPitTemperatureBottom() == null ? 0F : pitTemperatureTask.getInPitTemperatureBottom()));

            //升温速度=（发酵升温期的最高温度-入窖温度）/发酵升温期天数
            pitTemperatureTask.setToTopSpeedTop(pitTemperatureTask.getRisesTemperatureTop() / pitTemperatureTask.getToTopDaysTop());
            pitTemperatureTask.setToTopSpeedMiddle(pitTemperatureTask.getRisesTemperatureMiddle() / pitTemperatureTask.getToTopDaysMiddle());
            pitTemperatureTask.setToTopSpeedBottom(pitTemperatureTask.getRisesTemperatureBottom() / pitTemperatureTask.getToTopDaysBottom());

            //中挺天数=（发酵升温期的最大温度-0.5）>发酵温度  的天数
            Integer straightDaysTop = 0;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                if (pitTemperatureTask.getTopTemperatureTop() - 0.5 > (pitTemperatureTaskItem.getPitTemperatureTop() == null ? 0F : pitTemperatureTaskItem.getPitTemperatureTop())) {
                    straightDaysTop++;
                }
            }
            pitTemperatureTask.setStraightDaysTop(straightDaysTop);
            Integer straightDaysMiddle = 0;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                if (pitTemperatureTask.getTopTemperatureMiddle() - 0.5 > (pitTemperatureTaskItem.getPitTemperatureMiddle() == null ? 0F : pitTemperatureTaskItem.getPitTemperatureMiddle())) {
                    straightDaysMiddle++;
                }
            }
            pitTemperatureTask.setStraightDaysMiddle(straightDaysMiddle);
            Integer straightDaysBottom = 0;
            for (TPoWorkshopPitTemperatureTaskItems pitTemperatureTaskItem : pitTemperatureTaskItems) {
                if (pitTemperatureTask.getTopTemperatureBottom() - 0.5 > (pitTemperatureTaskItem.getPitTemperatureBottom() == null ? 0F : pitTemperatureTaskItem.getPitTemperatureBottom())) {
                    straightDaysBottom++;
                }
            }
            pitTemperatureTask.setStraightDaysBottom(straightDaysBottom);
        }
        return pitTemperatureTask;
    }

    /**
     * 报表的表头
     *
     * @return
     */
    private List<List<String>> head(TemTaskReportDTO temTaskReportDTO) {
        List<List<String>> list = new ArrayList<>();
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,中心".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,车间".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,窖号".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,封窖日期".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,地温".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,入窖平均温度".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,顶温度".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,入窖温度".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,升温幅度".split(","))));
        list.add(new ArrayList<>(Arrays.asList("窖内升温测试表,升至顶温天数".split(","))));
        // 查询窖池任务关联明细最大的条数
        Integer maxNum = workshopPitTemperatureTaskMapper.TemTaskMaxNum(temTaskReportDTO);
        if (maxNum != null) {
            for (int i = 1; i <= maxNum; i++) {
                list.add(new ArrayList<>(Arrays.asList(("窖内升温测试表,第" + i + "天").split(","))));
            }
        } else {
            list.add(new ArrayList<>(Arrays.asList(("窖内升温测试表,第x天").split(","))));
        }
        return list;
    }
}

class TemTaskStyleCellHandler extends AbstractRowWriteHandler {
    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row
            , Integer relativeRowIndex, Boolean isHead) {
        Row headRow = writeSheetHolder.getSheet().getRow(0);
        for (int i = 0; i < headRow.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell == null) {
                cell = row.createCell(i);
                CellStyle cellStyle = row.getSheet().getWorkbook().createCellStyle();
                cellStyle.setBorderLeft(BorderStyle.THIN); // 左边框
                cellStyle.setBorderTop(BorderStyle.THIN); // 上边框
                cellStyle.setBorderBottom(BorderStyle.THIN); // 下边框
                cellStyle.setBorderRight(BorderStyle.THIN); // 右边框
                cell.setCellStyle(cellStyle);
            }
        }
    }
}
