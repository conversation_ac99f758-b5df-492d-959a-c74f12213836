package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskDiscardMapper;
import com.hvisions.brewage.dto.tpo.TaskDiscardAddDTO;
import com.hvisions.brewage.dto.tpo.TaskDiscardQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskDiscard;
import com.hvisions.brewage.entity.tpo.TaskSpreading;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskDiscardVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskDiscardService
 * @description: 丢糟任务Service
 * @date 2025/7/18 15:25
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskDiscardService {

    @Resource
    private TaskDiscardMapper taskDiscardMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    /**
     * 查询丢糟任务任务列表
     * @param queryDTO
     * @return
     */
    public Page<TaskDiscardVO> findPageList(TaskDiscardQueryDTO queryDTO) {
        Page<TaskDiscardVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());

        List<TaskDiscardVO> inspectionVOList = taskDiscardMapper.selectPageList(page, queryDTO);

        page.setRecords(inspectionVOList);
        return page;
    }

    /**
     * 新增
     * @param addDTO
     * @return
     */
    public String add(TaskDiscardAddDTO addDTO) {
        log.info("调用新增丢糟任务列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增丢糟任务列表，获取到当前登录用户，用户id：{}", userId);

        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskDiscard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDiscard::getPitOrder,addDTO.getPitOrder())
                .eq(TaskDiscard::getDeleted,0);
        Integer count = taskDiscardMapper.selectCount(wrapper);
        if(count>0){
            return OperationResult.ADD_SUCCESS.getDescription();
           // FailureCode addFailed = FailureCode.ADD_FAILED_DATA_EXISTS;
           // throw new BaseKnownException(addFailed.getCode(), addFailed.getDescription(addDTO.getPitOrder()));
        }

        TaskDiscard taskDiscard = DtoMapper.convert(addDTO, TaskDiscard.class);

        //查询配置最大的流水号
        Integer maxNum = taskDiscardMapper.selectMaxTaskNo(ProdConfigEnum.DZ);
        String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.DZ, maxNum);
        taskDiscard.setTaskNo(taskNo);

        taskDiscard.setTaskName(TaskBusiness.TASK_DISCARD.getTaskName());
        taskDiscard.setTaskType(TaskBusiness.TASK_DISCARD.getTaskType());
        taskDiscard.setStatus(TaskStatus.PENDING_EXE.getName());
        taskDiscard.setCreateTime(new Date());
        taskDiscard.setCreatorId(userId);

        taskDiscardMapper.insert(taskDiscard);

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("删除丢糟任务列表，传入参数：{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("删除丢糟任务列表，获取到当前登录用户，用户id：{}", userId);

        taskDiscardMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 结束任务
     * @param pitOrder
     * @return
     */
    public String endTask(String pitOrder) {
        log.info("调用结束丢糟任务，传入参数-------》窖池订单号:{}", pitOrder);
        Integer userId = userAuditorAware.getUserId();
        log.info("调用结束丢糟任务，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskDiscard> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskDiscard::getPitOrder,pitOrder)
                .ne(TaskDiscard::getStatus,TaskStatus.TERMINATED.getName())
                .ne(TaskDiscard::getStatus,TaskStatus.COMPLETED.getName())
                .eq(TaskDiscard::getDeleted,0);
        List<TaskDiscard> discardList = taskDiscardMapper.selectList(wrapper);
        discardList.forEach(v->{
            log.info("调用结束丢糟任务,查询出需要结束的任务为：任务号------》{}",v.getTaskNo());
            v.setStatus(TaskStatus.COMPLETED.getName());
            v.setUpdaterId(userId);
            v.setUpdateTime(new Date());
            v.setEndTime(new Date());
            taskDiscardMapper.updateById(v);
        });

        return OperationResult.OTHER_SUCCESS.getDescription("结束丢糟任务成功");
    }
}
