package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskOrderBatchMapper;
import com.hvisions.brewage.dto.tpo.TaskOrderBatchQueryDTO;
import com.hvisions.brewage.entity.tpo.TaskOrderBatch;
import com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.UseMaterialBatchDTO;
import com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.TurnoverUseMaterialDTO;
import com.hvisions.brewage.mkwine.service.TPoWorkshopPitTurnoverOrderService;
import com.hvisions.brewage.vo.tpo.TaskOrderBatchVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskOrderBatchService
 * @description: 任务-用量批次Service
 * @date 2025/8/8 15:49
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskOrderBatchService {
    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TaskOrderBatchMapper taskOrderBatchMapper;

    @Resource
    private TPoWorkshopPitTurnoverOrderService tPoWorkshopPitTurnoverOrderService;


    /**
     * 添加数据---翻窖任务专用
     *
     * @param taskNo       任务号
     * @param taskName     任务名称
     * @param batchDTOList 批次信息
     * @param orderCode 窖池订单号
     * @param turnoverPotNum 翻窖完成后甑口数
     * @param inputType 投入类型 0.入窖投入;1.出窖投入;2.翻窖投入
     */
    public void addTurnoverUseBatch(String taskNo, String taskName, List<UseMaterialBatchDTO> batchDTOList,String orderCode,Integer turnoverPotNum,String taskMaterialType,String inputType) {
        log.info("添加任务-用量批次，传入参数，任务号：{}，任务名称：{}，参数：{}", taskNo, taskName, JSONObject.toJSONString(batchDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("获取到当前登录用户，用户id：{}", userId);

        //校验是否存在，存在则删除，在从新新增
        int size = verifyIsExist(taskNo, taskName).size();
        if (size > 0) {
            LambdaQueryWrapper<TaskOrderBatch> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskOrderBatch::getTaskNo, taskNo)
                    .eq(TaskOrderBatch::getTaskName, taskName)
                    .eq(TaskOrderBatch::getPitOrder, orderCode)
                    .eq(TaskOrderBatch::getDeleted, 0);
            taskOrderBatchMapper.delete(wrapper);
        }

        batchDTOList.forEach(v -> {
            TaskOrderBatch orderBatch = new TaskOrderBatch();
            orderBatch.setTaskNo(taskNo);
            orderBatch.setTaskName(taskName);
            orderBatch.setBatchTaskNo(v.getTaskNo());
            orderBatch.setBatch(v.getBatch());
            orderBatch.setUseCount(v.getUseCount());
            orderBatch.setRequireEquipment(v.getRequireEquipment());
            orderBatch.setMaterialCode(v.getMaterialCode());
            orderBatch.setMaterialId(v.getMaterielId());
            orderBatch.setMaterialName(v.getMaterialName());
            orderBatch.setUseId(userId);
            orderBatch.setUseTime(new Date());
            orderBatch.setCreatorId(userId);
            orderBatch.setCreateTime(new Date());
            orderBatch.setPitOrder(orderCode);
            orderBatch.setTaskMaterialType(taskMaterialType);
            orderBatch.setTurnoverPotNum(turnoverPotNum);
            orderBatch.setInputType(inputType);
            orderBatch.setDataId(v.getDataId());
            taskOrderBatchMapper.insert(orderBatch);
        });
    }


    /**
     * 添加数据-任务使用
     *
     * @param taskNo       任务号
     * @param taskName     任务名称
     * @param batchDTOList 批次信息
     * @param inputType 投入类型 0.入窖投入;1.出窖投入;2.翻窖投入
     */
    public void addTurnoverUseBatch(String taskNo, String taskName, List<UseMaterialBatchDTO> batchDTOList,String taskMaterialType,String inputType) {
        log.info("添加任务-用量批次，传入参数，任务号：{}，任务名称：{}，参数：{}", taskNo, taskName, JSONObject.toJSONString(batchDTOList));
        Integer userId = userAuditorAware.getUserId();
        log.info("获取到当前登录用户，用户id：{}", userId);

        //校验是否存在，存在则删除，在从新新增
        int size = verifyIsExist(taskNo, taskName).size();
        if (size > 0) {
            LambdaQueryWrapper<TaskOrderBatch> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TaskOrderBatch::getTaskNo, taskNo)
                    .eq(TaskOrderBatch::getTaskName, taskName)
                    .eq(TaskOrderBatch::getDeleted, 0);
            taskOrderBatchMapper.delete(wrapper);
        }

        batchDTOList.forEach(v -> {
            TaskOrderBatch orderBatch = new TaskOrderBatch();
            orderBatch.setTaskNo(taskNo);
            orderBatch.setTaskName(taskName);
            orderBatch.setBatchTaskNo(v.getTaskNo());
            orderBatch.setBatch(v.getBatch());
            orderBatch.setUseCount(v.getUseCount());
            orderBatch.setRequireEquipment(v.getRequireEquipment());
            orderBatch.setMaterialCode(v.getMaterialCode());
            orderBatch.setMaterialId(v.getMaterielId());
            orderBatch.setMaterialName(v.getMaterialName());
            orderBatch.setUseId(userId);
            orderBatch.setUseTime(new Date());
            orderBatch.setCreatorId(userId);
            orderBatch.setCreateTime(new Date());
            orderBatch.setTaskMaterialType(taskMaterialType);
            orderBatch.setInputType(inputType);
            orderBatch.setDataId(v.getDataId());
            taskOrderBatchMapper.insert(orderBatch);
        });
    }


    /**
     * 判断是否存在，存在则返回
     *
     * @param taskNo
     * @param taskName
     * @return
     */
    public List<UseMaterialBatchDTO> verifyIsExist(String taskNo, String taskName) {
        LambdaQueryWrapper<TaskOrderBatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskOrderBatch::getTaskNo, taskNo)
                .eq(TaskOrderBatch::getTaskName, taskName)
                .eq(TaskOrderBatch::getDeleted, 0);
        List<TaskOrderBatch> taskOrderBatches = taskOrderBatchMapper.selectList(wrapper);
        return DtoMapper.convertList(taskOrderBatches, UseMaterialBatchDTO.class);
    }


    /**
     * 调用原辅料接口，计算物料批次
     * @param taskNo 任务号|IOT任务号
     * @param taskName 任务名称
     * @param centerId 中心id
     * @param locationId 车间id
     * @param useCount 需求用量
     * @param lineName 区域名称（大曲时传）
     * @param type 物料类型
     * @param materialCode 物料编码
     * @param inputType 投入类型 0.入窖投入;1.出窖投入;2.翻窖投入
     */
    public void addTurnoverUseMaterial(String taskNo, String taskName,Integer centerId,Integer locationId,BigDecimal useCount,String lineName,String type,String materialCode,String inputType) {
        TurnoverUseMaterialDTO materialDTO = new  TurnoverUseMaterialDTO();
        materialDTO.setCenterId(centerId);
        materialDTO.setLocationId(locationId);
        materialDTO.setUseCount(useCount);
        //查询窖池订单的区域
        materialDTO.setLineName(lineName);
        materialDTO.setType(type);
        materialDTO.setMaterialCode(materialCode);
        log.info("调用原辅料接口，查询{}对应的用量批次，传递参数：{}",type,JSONObject.toJSONString(materialDTO));
        //开始调用接口
        List<UseMaterialBatchDTO> useBatchDTOList = verifyIsExist(taskNo, taskName);
        materialDTO.setBatchDTOList(useBatchDTOList);
        List<UseMaterialBatchDTO> batchDTOList = tPoWorkshopPitTurnoverOrderService.turnoverUseMaterial(materialDTO);
        log.info("收到调用原辅料接口查询{}返回值，返回信息：{}",type,JSONObject.toJSONString(batchDTOList));

        //添加数据
        addTurnoverUseBatch(taskNo,taskName,batchDTOList,type,inputType);
    }

    /**
     * 订单物料信息
     * @param queryDTO
     * @return
     */
    public List<TaskOrderBatchVO> getOrderBatchList(TaskOrderBatchQueryDTO queryDTO) {
        return taskOrderBatchMapper.selectListByOrder(queryDTO);
    }
}
