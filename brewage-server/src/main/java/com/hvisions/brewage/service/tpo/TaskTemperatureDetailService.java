package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskTemperatureDetailMapper;
import com.hvisions.brewage.dao.tpo.TaskTemperatureMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.TaskLoadingDetail;
import com.hvisions.brewage.entity.tpo.TaskTemperature;
import com.hvisions.brewage.entity.tpo.TaskTemperatureDetail;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.utils.BaseWrapper;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.vo.tpo.TaskLoadingDetailVO;
import com.hvisions.brewage.vo.tpo.TaskTemperatureDetailVO;
import com.hvisions.common.utils.DtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskTemperatureDetailService
 * @description: 窖池升温任务详情Service
 * @date 2025/7/14 16:12
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskTemperatureDetailService {

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TaskTemperatureDetailMapper taskTemperatureDetailMapper;

    @Resource
    private TaskTemperatureMapper taskTemperatureMapper;

    @Resource
    private BaseWrapper baseWrapper;

    /**
     * 查询窖池升温任务详情列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskTemperatureDetailVO> findPageList(TaskTemperatureDetailQueryDTO queryDTO) {
        LambdaQueryWrapper<TaskTemperatureDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskTemperatureDetail::getDeleted, 0)
                .eq(TaskTemperatureDetail::getTaskTemperatureId, queryDTO.getTaskTemperatureId());
        IPage<TaskTemperatureDetail> page = taskTemperatureDetailMapper.selectPage(new Page<>(queryDTO.getPage(), queryDTO.getPageSize()), wrapper);
        return baseWrapper.convertToPage(page, TaskTemperatureDetailVO.class);
    }


    /**
     * 新增
     *
     * @param addDTO
     * @return
     */
    public String add(TaskTemperatureDetailAddDTO addDTO) {
        log.info("调用新增窖池升温任务详情列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增窖池升温任务详情列表，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskTemperature> wrapper1 = new LambdaQueryWrapper<>();
        wrapper1.eq(TaskTemperature::getPitNo, addDTO.getPitNo())
                .in(TaskTemperature::getStatus, TaskStatus.PENDING_EXE.getName(), TaskStatus.IN_PROGRESS.getName())
                .eq(TaskTemperature::getDeleted, 0);
        TaskTemperature taskTemperature = taskTemperatureMapper.selectOne(wrapper1);


        //查询任务是否已经创建过了
        LambdaQueryWrapper<TaskTemperatureDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskTemperatureDetail::getPitNo, addDTO.getPitNo())
                .apply("DATE_FORMAT( record_date, '%Y-%m-%d' ) = DATE_FORMAT({0},'%Y-%m-%d')", addDTO.getRecordDate())
                .eq(TaskTemperatureDetail::getDeleted, 0);
        TaskTemperatureDetail temperatureDetail = taskTemperatureDetailMapper.selectOne(wrapper);

        //无则新增，有则修改
        if (null == temperatureDetail) {
            TaskTemperatureDetail temperatureDetailAdd = new TaskTemperatureDetail();
            if (null != taskTemperature) {
                temperatureDetailAdd.setTaskTemperatureId(taskTemperature.getId());
            }
            temperatureDetailAdd.setPitNo(addDTO.getPitNo());
            temperatureDetailAdd.setCenter(addDTO.getCenter());
            temperatureDetailAdd.setLocation(addDTO.getLocation());
            temperatureDetailAdd.setUpperTemp(addDTO.getUpperTemp());
            temperatureDetailAdd.setMiddleTemp(addDTO.getMiddleTemp());
            temperatureDetailAdd.setBottomTemp(addDTO.getBottomTemp());
            temperatureDetailAdd.setRecordDate(addDTO.getRecordDate());
            temperatureDetailAdd.setRecordTime(new Date());
            temperatureDetailAdd.setDataSource(addDTO.getDataSource());
            temperatureDetailAdd.setRemarks(addDTO.getRemarks());
            temperatureDetailAdd.setCreatorId(userId);
            temperatureDetailAdd.setCreateTime(new Date());
            taskTemperatureDetailMapper.insert(temperatureDetailAdd);
        } else {
            TaskTemperatureDetail convert = DtoMapper.convert(addDTO, TaskTemperatureDetail.class);
            if (null != taskTemperature && null==convert.getTaskTemperatureId()) {
                convert.setTaskTemperatureId(taskTemperature.getId());
            }
            convert.setId(temperatureDetail.getId());
            convert.setUpdateTime(new Date());
            convert.setUpdaterId(userId);
            convert.setRecordTime(new Date());
            taskTemperatureDetailMapper.updateById(convert);
        }

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 删除
     *
     * @param ids
     * @return
     */
    public String deleteIds(List<Integer> ids) {
        log.info("调用删除窖池升温任务详情列表，传入参数-------》{}", JSONObject.toJSONString(ids));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用删除窖池升温任务详情列表，获取到当前登录用户，用户id：{}", userId);

        taskTemperatureDetailMapper.deleteBatchIds(ids);

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 修改
     *
     * @param updateDTO
     * @return
     */
    public String update(TaskTemperatureDetailUpdateDTO updateDTO) {
        log.info("调用修改窖池升温任务详情列表，传入参数-------》{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用修改窖池升温任务详情列表，获取到当前登录用户，用户id：{}", userId);

        LambdaUpdateWrapper<TaskTemperatureDetail> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(TaskTemperatureDetail::getId, updateDTO.getId())
                .eq(TaskTemperatureDetail::getDeleted, 0)
                .set(TaskTemperatureDetail::getMiddleTemp, updateDTO.getMiddleTemp())
                .set(TaskTemperatureDetail::getRecordDate, updateDTO.getRecordDate())
                .set(TaskTemperatureDetail::getUpdaterId, userId)
                .set(TaskTemperatureDetail::getUpdateTime, new Date());

        taskTemperatureDetailMapper.update(null, wrapper);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }
}
