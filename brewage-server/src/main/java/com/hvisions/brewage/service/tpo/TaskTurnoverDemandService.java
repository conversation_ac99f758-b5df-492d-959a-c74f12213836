package com.hvisions.brewage.service.tpo;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.brewage.advice.UserAuditorAware;
import com.hvisions.brewage.dao.tpo.TaskTurnoverDemandMapper;
import com.hvisions.brewage.dao.tpo.TaskTurnoverDemandMaterialMapper;
import com.hvisions.brewage.dao.tpo.TaskTurnoverDemandOrdersMapper;
import com.hvisions.brewage.dto.tpo.*;
import com.hvisions.brewage.entity.tpo.*;
import com.hvisions.brewage.enums.*;
import com.hvisions.brewage.feign.powder.PowderQudouDemandClient;
import com.hvisions.brewage.powder.DemandBatchOperateDTO;
import com.hvisions.brewage.powder.DemandBatchOperateDetailDTO;
import com.hvisions.brewage.utils.DateUtil;
import com.hvisions.brewage.utils.FlowGenerationUtil;
import com.hvisions.brewage.validation.Validator;
import com.hvisions.brewage.vo.tpo.TaskTurnoverDemandListVO;
import com.hvisions.brewage.vo.tpo.TaskTurnoverDemandMaterialVO;
import com.hvisions.brewage.vo.tpo.TaskTurnoverDemandOrdersVO;
import com.hvisions.brewage.vo.tpo.TaskTurnoverDemandVO;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: TaskTurnoverDemandService
 * @description: 翻窖需求Service
 * @date 2025/7/24 16:34
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class TaskTurnoverDemandService {

    @Resource
    private TaskTurnoverDemandMapper taskTurnoverDemandMapper;

    @Resource
    private TaskTurnoverDemandOrdersMapper taskTurnoverDemandOrdersMapper;

    @Resource
    private TaskTurnoverDemandMaterialMapper taskTurnoverDemandMaterialMapper;

    @Resource
    private UserAuditorAware userAuditorAware;

    @Resource
    private TPoTaskTurnoverDemandHjService tPoTaskTurnoverDemandHjService;

    @Resource
    private PowderQudouDemandClient powderQudouDemandClient;


    /**
     * 查询翻窖需求列表
     *
     * @param queryDTO
     * @return
     */
    public Page<TaskTurnoverDemandVO> findPageList(TaskTurnoverDemandQueryDTO queryDTO) {
        Page<TaskTurnoverDemandVO> page = new Page<>(queryDTO.getPage(), queryDTO.getPageSize());
        List<TaskTurnoverDemandVO> list = taskTurnoverDemandMapper.selectPageList(page, queryDTO);
        page.setRecords(list);
        return page;
    }

    /**
     * 新增翻窖需求列表
     *
     * @param addDTO
     * @return
     */
    public String add(TaskTurnoverDemandAddDTO addDTO) {
        //校验参数不能为空
        Validator<TaskTurnoverDemandMaterialAddDTO> validatorParameter = new Validator<>();
        validatorParameter.validate(addDTO.getMaterialList());

        Validator<TaskTurnoverDemandOrdersAddDTO> validatorParameter1 = new Validator<>();
        validatorParameter1.validate(addDTO.getOrdersAddDTOList());


        log.info("调用新增翻窖需求列表，传入参数-------》{}", JSONObject.toJSONString(addDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("调用新增翻窖需求列表，获取到当前登录用户，用户id：{}", userId);

        //分组生成对应的单子
        Map<String, List<TaskTurnoverDemandOrdersAddDTO>> listMap = addDTO.getOrdersAddDTOList().stream().collect(Collectors.groupingBy(item -> item.getCenterId() + "|" + item.getLocationId()));
        listMap.forEach((k, v) -> {
            String[] split = k.split("\\|");
            Integer centerId = Integer.parseInt(split[0]);
            Integer locationId = Integer.parseInt(split[1]);

            //创建单子
            TaskTurnoverDemand taskTurnoverDemand = new TaskTurnoverDemand();

            //查询配置最大的流水号
            Integer maxNum = taskTurnoverDemandMapper.selectMaxTaskNo(ProdConfigEnum.FJ);
            String taskNo = FlowGenerationUtil.generationYH(ProdConfigEnum.FJ, maxNum);
            taskTurnoverDemand.setTaskNo(taskNo);

            taskTurnoverDemand.setCenterId(centerId);
            taskTurnoverDemand.setLocationId(locationId);
            taskTurnoverDemand.setStartTime(addDTO.getStartTime());
            taskTurnoverDemand.setEndTime(addDTO.getEndTime());
            taskTurnoverDemand.setDemandType(addDTO.getDemandType());
            taskTurnoverDemand.setCreatorId(userId);
            taskTurnoverDemand.setCreateTime(new Date());
            taskTurnoverDemandMapper.insert(taskTurnoverDemand);


            //添加翻窖需求窖池订单
            List<TaskTurnoverDemandOrders> demandOrdersList = DtoMapper.convertList(v, TaskTurnoverDemandOrders.class);
            demandOrdersList.forEach(v2 -> {
                v2.setTaskTurnoverDemandId(taskTurnoverDemand.getId());
                v2.setCreatorId(userId);
                v2.setCreateTime(new Date());
                taskTurnoverDemandOrdersMapper.insert(v2);
            });


            //添加翻窖需求物料明细
            List<TaskTurnoverDemandMaterialAddDTO> collect = addDTO.getMaterialList().stream().filter(item -> item.getLocationId().equals(locationId) && item.getCenterId().equals(centerId)).collect(Collectors.toList());

            //添加翻窖需求窖池订单
            List<TaskTurnoverDemandMaterial> demandMaterialList = DtoMapper.convertList(collect, TaskTurnoverDemandMaterial.class);
            demandMaterialList.forEach(v2 -> {
                v2.setStatus(TaskStatus.UNREPORTED.getName());
                v2.setTaskTurnoverDemandId(taskTurnoverDemand.getId());
                v2.setCreatorId(userId);
                v2.setCreateTime(new Date());
                taskTurnoverDemandMaterialMapper.insert(v2);
            });


            //修改物料总量
            updateMaterialNumOrState(taskTurnoverDemand, demandMaterialList);

            //提交需求,进行需求发放
            submitDemand(taskTurnoverDemand.getId());
        });

        return OperationResult.ADD_SUCCESS.getDescription();
    }

    /**
     * 修改物料总量
     *
     * @param taskTurnoverDemand
     * @param demandMaterialList
     */
    public void updateMaterialNumOrState(TaskTurnoverDemand taskTurnoverDemand, List<TaskTurnoverDemandMaterial> demandMaterialList) {
        //计算总量
        BigDecimal demandTotal = demandMaterialList.stream().map(order -> order.getApplicationDosage() != null ? order.getApplicationDosage() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        taskTurnoverDemand.setDemandTotal(demandTotal);
        taskTurnoverDemandMapper.updateById(taskTurnoverDemand);
    }

    /**
     * 删除翻窖需求详情列表
     *
     * @param detailsId
     * @return
     */
    public String deleteIdMaterial(Integer detailsId) {
        log.info("删除翻窖需求详情列表，传入参数id：{}", detailsId);
        Integer userId = userAuditorAware.getUserId();
        log.info("删除翻窖需求详情列表，获取到当前登录用户，用户id：{}", userId);

        TaskTurnoverDemandMaterial demandMaterial = taskTurnoverDemandMaterialMapper.selectById(detailsId);
        if (null == demandMaterial || demandMaterial.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("翻窖需求物料明细ID=" + detailsId));
        }

        //校验状态是否已提报
        if (demandMaterial.getStatus().equals(TaskStatus.REPORTED.getName())) {
            FailureCode updateFailed = FailureCode.DELETE_FAILED_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("物料需求已提报"));
        }

        TaskTurnoverDemand taskTurnoverDemand = taskTurnoverDemandMapper.selectById(demandMaterial.getTaskTurnoverDemandId());

        //如果状态是已驳回，则需要删除对应的需求发放单
        if(demandMaterial.getStatus().equals(TaskStatus.REJECTED.getName())){
            //删除曲粉需求
            if (taskTurnoverDemand.getDemandType().equals(OtherEnum.QF_CONTAINS)) {
                log.info("调用原辅料删除曲粉需求，参数：任务号：{}，需求模式:{}",taskTurnoverDemand.getTaskNo(),demandMaterial.getPattern());
                powderQudouDemandClient.delTurnoverDemandDetail(taskTurnoverDemand.getTaskNo(),demandMaterial.getArea());
            }

            //删除回酒需求
            if (taskTurnoverDemand.getDemandType().equals(OtherEnum.HJ_CONTAINS)) {
                log.info("调用原辅料删除回酒需求，参数：任务号：{}，需求模式:{}",taskTurnoverDemand.getTaskNo(),demandMaterial.getPattern());
                tPoTaskTurnoverDemandHjService.deleteByTask(taskTurnoverDemand.getTaskNo(),demandMaterial.getPattern());
            }
        }

        taskTurnoverDemandMaterialMapper.deleteById(detailsId);

        //判断明细是否已删除完，明细删除完则删除主任务单
        LambdaQueryWrapper<TaskTurnoverDemandMaterial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskTurnoverDemandMaterial::getTaskTurnoverDemandId,taskTurnoverDemand.getId())
                .eq(TaskTurnoverDemandMaterial::getDeleted,0);
        Integer count = taskTurnoverDemandMaterialMapper.selectCount(wrapper);
        //无明细数据了，删除主任务
        if(count==0){
            taskTurnoverDemandMapper.deleteById(taskTurnoverDemand.getId());
        }

        return OperationResult.DELETE_SUCCESS.getDescription();
    }

    /**
     * 查询需求单详情-需求单信息
     *
     * @param id 翻窖需求主键id
     * @return
     */
    public TaskTurnoverDemandListVO findDemandList(Integer id) {
        return taskTurnoverDemandMapper.selectDemandList(id);
    }

    /**
     * 查询需求单详情-订单明细
     *
     * @param id 翻窖需求主键id
     * @return
     */
    public List<TaskTurnoverDemandOrdersVO> findDemandOrdersList(Integer id) {
        return taskTurnoverDemandOrdersMapper.selectOrderList(id);
    }

    /**
     * 查询需求单详情-物料明细
     *
     * @param id 翻窖需求主键id
     * @return
     */
    public List<TaskTurnoverDemandMaterialVO> findDemandMaterialList(Integer id) {
        return taskTurnoverDemandMaterialMapper.selectDemandMaterialList(id);
    }

    /**
     * 修改需求单详情-物料明细
     *
     * @param updateDTO
     * @return
     */
    public String updateDemandMaterial(TaskTurnoverDemandMaterialUpdateDTO updateDTO) {
        log.info("修改翻窖需求列表-需求单详情-物料明细，传入参数：{}", JSONObject.toJSONString(updateDTO));
        Integer userId = userAuditorAware.getUserId();
        log.info("修改翻窖需求列表-需求单详情-物料明细，获取到当前登录用户，用户id：{}", userId);
        TaskTurnoverDemand taskTurnoverDemand = taskTurnoverDemandMapper.selectById(updateDTO.getId());
        if (null == taskTurnoverDemand || taskTurnoverDemand.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("翻窖需求ID=" + updateDTO.getId()));
        }

        TaskTurnoverDemandMaterial demandMaterial = taskTurnoverDemandMaterialMapper.selectById(updateDTO.getDetailsId());
        if (null == demandMaterial || demandMaterial.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("翻窖需求物料明细ID=" + updateDTO.getDetailsId()));
        }

        //校验状态是否已提报
        if (demandMaterial.getStatus().equals(TaskStatus.REPORTED.getName())) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_EXISTS_MSG;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("物料需求已提报"));
        }


        demandMaterial.setApplicationDosage(updateDTO.getApplicationDosage());
        demandMaterial.setUpdaterId(userId);
        demandMaterial.setUpdateTime(new Date());
        taskTurnoverDemandMaterialMapper.updateById(demandMaterial);


        //修改物料总量
        LambdaQueryWrapper<TaskTurnoverDemandMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskTurnoverDemandMaterial::getDeleted, 0)
                .eq(TaskTurnoverDemandMaterial::getTaskTurnoverDemandId, taskTurnoverDemand.getId());
        List<TaskTurnoverDemandMaterial> demandMaterialList = taskTurnoverDemandMaterialMapper.selectList(queryWrapper);
        updateMaterialNumOrState(taskTurnoverDemand, demandMaterialList);

        return OperationResult.UPDATE_SUCCESS.getDescription();
    }

    /**
     * 提交翻窖物料需求发放-整单提交-单条
     *
     * @param id 翻窖需求主键id
     * @return
     */
    public String submitDemand(Integer id) {
        log.info("提交翻窖物料需求发放-整单提交-单条，传入参数,翻窖需求主键id:{}", id);
        Integer userId = userAuditorAware.getUserId();
        log.info("提交翻窖物料需求发放-整单提交-单条，获取到当前登录用户，用户id：{}", userId);

        TaskTurnoverDemand taskTurnoverDemand = taskTurnoverDemandMapper.selectById(id);
        if (null == taskTurnoverDemand || taskTurnoverDemand.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("翻窖需求ID=" + id));
        }

        //是曲粉类型。调用曲粉需求提报接口
        if (taskTurnoverDemand.getDemandType().equals(OtherEnum.QF_CONTAINS)) {
            log.info("执行调用曲粉需求逻辑");
            sendPowderQudouDemand(taskTurnoverDemand, null, userId);
        }

        //是回酒类型。调用回酒需求提报接口
        if (taskTurnoverDemand.getDemandType().equals(OtherEnum.HJ_CONTAINS)) {
            log.info("执行调用回酒需求逻辑");
            sendTPoTaskTurnoverDemandHj(taskTurnoverDemand, null, userId);
        }

        return OperationResult.OTHER_SUCCESS.getDescription("提交需求");
    }

    /**
     * 提交翻窖物料需求发放-整单提交-批量
     *
     * @param ids 翻窖需求主键id
     * @return
     */
    public String submitDemandList(List<Integer> ids) {
        log.info("提交翻窖物料需求发放-整单提交-批量，传入参数,翻窖需求主键id:{}", ids);
        Integer userId = userAuditorAware.getUserId();
        log.info("提交翻窖物料需求发放-整单提交-批量，获取到当前登录用户，用户id：{}", userId);

        ids.forEach(this::submitDemand);

        return OperationResult.OTHER_SUCCESS.getDescription("提交需求");
    }


    /**
     * 提交翻窖物料需求发放-提交某一项物料
     *
     * @param id 翻窖需求主键id
     * @param detailsId 翻窖需求物料明细Id
     * @return
     */
    public String submitDemandMaterial(Integer id, Integer detailsId) {
        log.info("提交翻窖物料需求发放-提交某一项物料，传入参数,翻窖需求主键id:{},翻窖需求物料明细Id:{}", id,detailsId);
        Integer userId = userAuditorAware.getUserId();
        log.info("提交翻窖物料需求发放-提交某一项物料，获取到当前登录用户，用户id：{}", userId);

        TaskTurnoverDemand taskTurnoverDemand = taskTurnoverDemandMapper.selectById(id);
        if (null == taskTurnoverDemand || taskTurnoverDemand.getDeleted()) {
            FailureCode updateFailed = FailureCode.UPDATE_FAILED_DATA_NOT_EXISTS;
            throw new BaseKnownException(updateFailed.getCode(), updateFailed.getDescription("翻窖需求ID=" + id));
        }

        //是曲粉类型。调用曲粉需求提报接口
        if (taskTurnoverDemand.getDemandType().equals(OtherEnum.QF_CONTAINS)) {
            log.info("执行调用曲粉需求逻辑");
            sendPowderQudouDemand(taskTurnoverDemand, detailsId, userId);
        }

        //是回酒类型。调用回酒需求提报接口
        if (taskTurnoverDemand.getDemandType().equals(OtherEnum.HJ_CONTAINS)) {
            log.info("执行调用回酒需求逻辑");
            sendTPoTaskTurnoverDemandHj(taskTurnoverDemand, detailsId, userId);
        }

        return OperationResult.OTHER_SUCCESS.getDescription("提交需求");
    }


    /**
     * 提交回酒需求
     *
     * @param taskTurnoverDemand 翻窖需求列表
     * @param detailsId          翻窖需求物料明细ID
     * @param userId
     */
    public void sendTPoTaskTurnoverDemandHj(TaskTurnoverDemand taskTurnoverDemand, Integer detailsId, Integer userId) {
        LambdaQueryWrapper<TaskTurnoverDemandMaterial> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskTurnoverDemandMaterial::getDeleted, 0)
                .eq(null != detailsId, TaskTurnoverDemandMaterial::getId, detailsId)
                .in(TaskTurnoverDemandMaterial::getStatus, TaskStatus.UNREPORTED.getName(), TaskStatus.REJECTED.getName())
                .eq(TaskTurnoverDemandMaterial::getTaskTurnoverDemandId, taskTurnoverDemand.getId());
        List<TaskTurnoverDemandMaterial> demandMaterialList = taskTurnoverDemandMaterialMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(demandMaterialList)) {
            demandMaterialList.forEach(v -> {
                //调用回酒需求提报接口
                TPoTaskTurnoverDemandHj tPoTaskTurnoverDemandHj = new TPoTaskTurnoverDemandHj();
                tPoTaskTurnoverDemandHj.setCenterId(v.getCenterId());
                tPoTaskTurnoverDemandHj.setLocationId(v.getLocationId());
                tPoTaskTurnoverDemandHj.setTaskNo(taskTurnoverDemand.getTaskNo());
                tPoTaskTurnoverDemandHj.setStartTime(taskTurnoverDemand.getStartTime());
                tPoTaskTurnoverDemandHj.setEndTime(taskTurnoverDemand.getEndTime());
                tPoTaskTurnoverDemandHj.setDemandQuantity(v.getApplicationDosage());
                tPoTaskTurnoverDemandHj.setPattern(v.getPattern());
                tPoTaskTurnoverDemandHj.setCreatorId(userId);
                log.info("提交翻窖物料需求发放-调用回酒需求提报接口，传入参数-----》{}", JSONObject.toJSONString(tPoTaskTurnoverDemandHj));
                tPoTaskTurnoverDemandHjService.add(tPoTaskTurnoverDemandHj);

                //修改提报状态
                v.setStatus(TaskStatus.REPORTED.getName());
                v.setUpdateTime(new Date());
                v.setUpdaterId(userId);
                taskTurnoverDemandMaterialMapper.updateById(v);
            });
        }
        log.info("提交翻窖物料需求发放-回酒需求提报完成，任务号：{}", taskTurnoverDemand.getTaskNo());
    }


    /**
     * 提交曲粉需求
     *
     * @param taskTurnoverDemand 翻窖需求列表
     * @param detailsId          翻窖需求物料明细ID
     * @param userId
     */
    public void sendPowderQudouDemand(TaskTurnoverDemand taskTurnoverDemand, Integer detailsId, Integer userId) {
        //调用曲粉需求提报接口-待提报的
        List<TaskTurnoverDemandMaterialVO> materialVOList = taskTurnoverDemandMaterialMapper.selectDemandMaterial(taskTurnoverDemand.getId(), detailsId);
        if (!CollectionUtils.isEmpty(materialVOList)) {
            materialVOList.forEach(materialVO -> {
                DemandBatchOperateDTO operateDTO = new DemandBatchOperateDTO();
                operateDTO.setCenterId(taskTurnoverDemand.getCenterId());
                operateDTO.setCenterName(materialVO.getCenterName());
                operateDTO.setBaseId(1);
                operateDTO.setTaskNo(taskTurnoverDemand.getTaskNo());
                operateDTO.setBaseName("黄舣酿酒生态园");
                operateDTO.setUseTime(DateUtil.currentDate() + " 00:00:00");
                operateDTO.setIsSubmit(true);
                List<DemandBatchOperateDetailDTO> detailDTOS = new ArrayList<>();
                DemandBatchOperateDetailDTO detailDTO = new DemandBatchOperateDetailDTO();
                BigDecimal[] result = divideAndRemainder(materialVO.getApplicationDosage());
                BigDecimal quotient = result[0];  // 商
                BigDecimal remainder = result[1]; // 余数

                detailDTO.setMaterialId(materialVO.getMaterialId());
                detailDTO.setMaterialCode(materialVO.getMaterialCode());
                detailDTO.setMaterialName(materialVO.getMaterialName());
                detailDTO.setDemandNumber(quotient);
                detailDTO.setLocationId(taskTurnoverDemand.getLocationId());
                detailDTO.setLocationName(materialVO.getLocationName());
                detailDTO.setLineName(materialVO.getArea());
                detailDTO.setOperateState("0");
                detailDTO.setType("2");
                detailDTO.setSpecificationId(materialVO.getSpecificationId());
                detailDTO.setSurplusNumber(remainder);
                detailDTO.setExperimentPower("0");
                detailDTO.setSciencePower("0");
                detailDTO.setUrgent("0");

                detailDTOS.add(detailDTO);
                operateDTO.setDetailDTOS(detailDTOS);

                log.info("提交翻窖物料需求发放-调用曲粉需求提报接口，传入参数-----》{}", JSONObject.toJSONString(operateDTO));
                ResultVO resultVO = powderQudouDemandClient.batchOperateQudouDemand(operateDTO);
                if (resultVO.getCode() != 200) {
                    log.info("提交翻窖物料需求发放-原辅料那边返回异常，任务号：{}，异常原因：{}", taskTurnoverDemand.getTaskNo(), resultVO.getMessage());
                    throw new BaseKnownException(resultVO.getCode(), resultVO.getMessage());
                }

                log.info("提交翻窖物料需求发放-曲粉需求提报完成，任务号：{}，跨号：{}", taskTurnoverDemand.getTaskNo(), materialVO.getArea());


                //修改提报状态
                TaskTurnoverDemandMaterial demandMaterial = DtoMapper.convert(materialVO, TaskTurnoverDemandMaterial.class);
                demandMaterial.setStatus(TaskStatus.REPORTED.getName());
                demandMaterial.setUpdateTime(new Date());
                demandMaterial.setUpdaterId(userId);
                taskTurnoverDemandMaterialMapper.updateById(demandMaterial);
            });
        }
    }

    /**
     * 算曲粉总袋数，估计45kg一带
     *
     * @param number
     * @return
     */
    public static BigDecimal[] divideAndRemainder(BigDecimal number) {
        BigDecimal divisor = new BigDecimal("45");
        BigDecimal quotient = number.divide(divisor, 0, RoundingMode.DOWN); // 商（整数部分）
        BigDecimal remainder = number.remainder(divisor); // 余数
        return new BigDecimal[]{quotient, remainder};
    }

    /**
     * 驳回任务
     *
     * @param taskNo
     * @param rejectedReason
     * @return
     */
    public String rejectedTask(String taskNo, String area,String pattern, String rejectedReason) {
        log.info("驳回翻窖物料需求发放，传入参数,翻窖需求认好号:{}，区域：{}，模式：{}，驳回原因:{}", taskNo, area,pattern, rejectedReason);
        Integer userId = userAuditorAware.getUserId();
        log.info("提交翻窖物料需求发放，获取到当前登录用户，用户id：{}", userId);

        LambdaQueryWrapper<TaskTurnoverDemand> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskTurnoverDemand::getTaskNo, taskNo)
                .eq(TaskTurnoverDemand::getDeleted, 0);
        TaskTurnoverDemand taskTurnoverDemand = taskTurnoverDemandMapper.selectOne(wrapper);
        if (null != taskTurnoverDemand) {
            LambdaUpdateWrapper<TaskTurnoverDemandMaterial> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(TaskTurnoverDemandMaterial::getTaskTurnoverDemandId, taskTurnoverDemand.getId())
                    //曲粉传递了跨，则需要将对应的跨改为驳回
                    .eq(null!=area, TaskTurnoverDemandMaterial::getArea, area)
                    .eq(null!=pattern, TaskTurnoverDemandMaterial::getPattern, pattern)
                    .eq(TaskTurnoverDemandMaterial::getDeleted, 0)
                    .set(TaskTurnoverDemandMaterial::getStatus, TaskStatus.REJECTED.getName())
                    .set(TaskTurnoverDemandMaterial::getUpdaterId, userId)
                    .set(TaskTurnoverDemandMaterial::getUpdateTime, new Date());
            taskTurnoverDemandMaterialMapper.update(null, updateWrapper);
        }

        return OperationResult.OTHER_SUCCESS.getDescription("驳回任务");
    }

}
