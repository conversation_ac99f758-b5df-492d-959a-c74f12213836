<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.mkwine.dao.TurnOverPitManage.TPoWorkshopPitTurnoverOrderMapper">
    <resultMap id="BaseResultMap"
               type="com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrder">
        <!--@mbg.generated-->
        <!--@Table t_po_workshop_pit_turnover_order-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="turnover_order_code" jdbcType="VARCHAR" property="turnoverOrderCode"/>
        <result column="center_id" jdbcType="INTEGER" property="centerId"/>
        <result column="location_id" jdbcType="INTEGER" property="locationId"/>
        <result column="vinasse_id" jdbcType="INTEGER" property="vinasseId"/>
        <result column="crew_id" jdbcType="INTEGER" property="crewId"/>
        <result column="shift_id" jdbcType="INTEGER" property="shiftId"/>
        <result column="formula_id" jdbcType="INTEGER" property="formulaId"/>
        <result column="plan_date" jdbcType="TIMESTAMP" property="planDate"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="actual_starttime" jdbcType="TIMESTAMP" property="actualStarttime"/>
        <result column="actual_endtime" jdbcType="TIMESTAMP" property="actualEndtime"/>
        <result column="notes" jdbcType="VARCHAR" property="notes"/>
        <result column="pots_count" jdbcType="INTEGER" property="potsCount"/>
        <result column="qu_quantity" jdbcType="FLOAT" property="quQuantity"/>
        <result column="back_alcoholic_quantity" jdbcType="FLOAT" property="backAlcoholicQuantity"/>
        <result column="is_delete" jdbcType="BIT" property="isDelete"/>
        <result column="plan_user_ids" jdbcType="VARCHAR" property="planUserIds"/>
        <result column="user_ids" jdbcType="VARCHAR" property="userIds"/>
        <result column="remain_num" jdbcType="INTEGER" property="remainNum"/>
        <result column="double_turnover_id" jdbcType="INTEGER" property="doubleTurnoverId"/>
        <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime"/>
        <result column="update_vinasse_id" jdbcType="INTEGER" property="updateVinasseId"/>
        <result column="update_vinasse_name" jdbcType="VARCHAR" property="updateVinasseName"/>
        <result column="qu_quantity_single" jdbcType="FLOAT" property="quQuantitySingle"/>
        <result column="back_alcoholic_quantity_single" jdbcType="FLOAT" property="backAlcoholicQuantitySingle"/>
        <result column="hs_quantity_single" jdbcType="FLOAT" property="hsQuantitySingle"/>
        <result column="wj_quantity_single" jdbcType="FLOAT" property="wjQuantitySingle"/>
        <result column="sdj_quantity_single" jdbcType="FLOAT" property="sdjQuantitySingle"/>
        <result column="past_back_alcoholic_quantity_single" jdbcType="FLOAT" property="pastBackAlcoholicQuantitySingle"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="creator_id" jdbcType="INTEGER" property="creatorId"/>
        <result column="updater_id" jdbcType="INTEGER" property="updaterId"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="turnover_times" jdbcType="INTEGER" property="turnoverTimes"/>
        <result column="hs_quantity" jdbcType="FLOAT" property="hsQuantity"/>
        <result column="wj_quantity" jdbcType="FLOAT" property="wjQuantity"/>
        <result column="sdj_quantity" jdbcType="FLOAT" property="sdjQuantity"/>
        <result column="orderPlanNum" jdbcType="INTEGER" property="orderPlanNum"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, turnover_order_code, center_id, location_id, vinasse_id, crew_id, shift_id, formula_id,
        plan_date, order_status, actual_starttime, actual_endtime, notes, pots_count, qu_quantity,
        back_alcoholic_quantity, is_delete, plan_user_ids, user_ids, remain_num, double_turnover_id,audit_time,
        vinasse_id,
        update_vinasse_id,
        update_vinasse_name,
        qu_quantity_single,
        back_alcoholic_quantity_single,
        hs_quantity_single,
        wj_quantity_single,
        sdj_quantity_single,create_time,update_time,creator_id,updater_id,turnover_times,
        hs_quantity,wj_quantity,sdj_quantity
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrder"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_po_workshop_pit_turnover_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="turnoverOrderCode != null">
                turnover_order_code,
            </if>
            <if test="centerId != null">
                center_id,
            </if>
            <if test="locationId != null">
                location_id,
            </if>
            <if test="vinasseId != null">
                vinasse_id,
            </if>
            <if test="crewId != null">
                crew_id,
            </if>
            <if test="shiftId != null">
                shift_id,
            </if>
            <if test="formulaId != null">
                formula_id,
            </if>
            <if test="planDate != null">
                plan_date,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="actualStarttime != null">
                actual_starttime,
            </if>
            <if test="actualEndtime != null">
                actual_endtime,
            </if>
            <if test="notes != null">
                notes,
            </if>
            <if test="potsCount != null">
                pots_count,
            </if>
            <if test="quQuantity != null">
                qu_quantity,
            </if>
            <if test="backAlcoholicQuantity != null">
                back_alcoholic_quantity,
            </if>
            <if test="isDelete != null">
                is_delete,
            </if>
            <if test="planUserIds != null">
                plan_user_ids,
            </if>
            <if test="userIds != null">
                user_ids,
            </if>
            <if test="remainNum != null">
                remain_num,
            </if>
            <if test="doubleTurnoverId != null">
                double_turnover_id,
            </if>
            <if test="auditTime != null">
                audit_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="turnoverOrderCode != null">
                #{turnoverOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="centerId != null">
                #{centerId,jdbcType=INTEGER},
            </if>
            <if test="locationId != null">
                #{locationId,jdbcType=INTEGER},
            </if>
            <if test="vinasseId != null">
                #{vinasseId,jdbcType=INTEGER},
            </if>
            <if test="crewId != null">
                #{crewId,jdbcType=INTEGER},
            </if>
            <if test="shiftId != null">
                #{shiftId,jdbcType=INTEGER},
            </if>
            <if test="formulaId != null">
                #{formulaId,jdbcType=INTEGER},
            </if>
            <if test="planDate != null">
                #{planDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="actualStarttime != null">
                #{actualStarttime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndtime != null">
                #{actualEndtime,jdbcType=TIMESTAMP},
            </if>
            <if test="notes != null">
                #{notes,jdbcType=VARCHAR},
            </if>
            <if test="potsCount != null">
                #{potsCount,jdbcType=INTEGER},
            </if>
            <if test="quQuantity != null">
                #{quQuantity,jdbcType=FLOAT},
            </if>
            <if test="backAlcoholicQuantity != null">
                #{backAlcoholicQuantity,jdbcType=FLOAT},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="planUserIds != null">
                #{planUserIds,jdbcType=VARCHAR},
            </if>
            <if test="userIds != null">
                #{userIds,jdbcType=VARCHAR},
            </if>
            <if test="remainNum != null">
                #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="doubleTurnoverId != null">
                #{doubleTurnoverId,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                #{auditTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.hvisions.brewage.mkwine.entity.TurnOverPitManage.TPoWorkshopPitTurnoverOrder">
        <!--@mbg.generated-->
        update t_po_workshop_pit_turnover_order
        <set>
            <if test="turnoverOrderCode != null">
                turnover_order_code = #{turnoverOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="centerId != null">
                center_id = #{centerId,jdbcType=INTEGER},
            </if>
            <if test="locationId != null">
                location_id = #{locationId,jdbcType=INTEGER},
            </if>
            <if test="vinasseId != null">
                vinasse_id = #{vinasseId,jdbcType=INTEGER},
            </if>
            <if test="crewId != null">
                crew_id = #{crewId,jdbcType=INTEGER},
            </if>
            <if test="shiftId != null">
                shift_id = #{shiftId,jdbcType=INTEGER},
            </if>
            <if test="formulaId != null">
                formula_id = #{formulaId,jdbcType=INTEGER},
            </if>
            <if test="planDate != null">
                plan_date = #{planDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="actualStarttime != null">
                actual_starttime = #{actualStarttime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndtime != null">
                actual_endtime = #{actualEndtime,jdbcType=TIMESTAMP},
            </if>
            <if test="notes != null">
                notes = #{notes,jdbcType=VARCHAR},
            </if>
            <if test="potsCount != null">
                pots_count = #{potsCount,jdbcType=INTEGER},
            </if>
            <if test="quQuantity != null">
                qu_quantity = #{quQuantity,jdbcType=FLOAT},
            </if>
            <if test="backAlcoholicQuantity != null">
                back_alcoholic_quantity = #{backAlcoholicQuantity,jdbcType=FLOAT},
            </if>
            <if test="isDelete != null">
                is_delete = #{isDelete,jdbcType=BIT},
            </if>
            <if test="planUserIds != null">
                plan_user_ids = #{planUserIds,jdbcType=VARCHAR},
            </if>
            <if test="userIds != null">
                user_ids = #{userIds,jdbcType=VARCHAR},
            </if>
            <if test="remainNum != null">
                remain_num = #{remainNum,jdbcType=INTEGER},
            </if>
            <if test="doubleTurnoverId != null">
                double_turnover_id = #{doubleTurnoverId,jdbcType=INTEGER},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updaterId != null">
                updater_id = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update t_po_workshop_pit_turnover_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="turnover_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.turnoverOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="center_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.centerId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="location_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.locationId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="vinasse_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.vinasseId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="crew_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.crewId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="shift_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.shiftId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="formula_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.formulaId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="plan_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.planDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="order_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.orderStatus,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="actual_starttime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.actualStarttime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="actual_endtime = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.actualEndtime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="notes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.notes,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="pots_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.potsCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="qu_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.quQuantity,jdbcType=FLOAT}
                </foreach>
            </trim>
            <trim prefix="back_alcoholic_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.backAlcoholicQuantity,jdbcType=FLOAT}
                </foreach>
            </trim>
            <trim prefix="is_delete = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="plan_user_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.planUserIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="user_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.userIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remain_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.remainNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="double_turnover_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.doubleTurnoverId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="audit_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.auditTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into t_po_workshop_pit_turnover_order
                (turnover_order_code, center_id, location_id, vinasse_id, crew_id, shift_id, formula_id,
                 plan_date, order_status, actual_starttime, actual_endtime, notes, pots_count, qu_quantity,
                 back_alcoholic_quantity, is_delete, plan_user_ids, user_ids, remain_num, double_turnover_id,
                 audit_time)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.turnoverOrderCode,jdbcType=VARCHAR}, #{item.centerId,jdbcType=INTEGER},
             #{item.locationId,jdbcType=INTEGER},
             #{item.vinasseId,jdbcType=INTEGER}, #{item.crewId,jdbcType=INTEGER}, #{item.shiftId,jdbcType=INTEGER},
             #{item.formulaId,jdbcType=INTEGER}, #{item.planDate,jdbcType=TIMESTAMP},
             #{item.orderStatus,jdbcType=INTEGER},
             #{item.actualStarttime,jdbcType=TIMESTAMP}, #{item.actualEndtime,jdbcType=TIMESTAMP},
             #{item.notes,jdbcType=VARCHAR}, #{item.potsCount,jdbcType=INTEGER}, #{item.quQuantity,jdbcType=FLOAT},
             #{item.backAlcoholicQuantity,jdbcType=FLOAT}, #{item.isDelete,jdbcType=BIT},
             #{item.planUserIds,jdbcType=VARCHAR},
             #{item.userIds,jdbcType=VARCHAR}, #{item.remainNum,jdbcType=INTEGER},
             #{item.doubleTurnoverId,jdbcType=INTEGER},
             #{item.auditTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <update id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        update t_po_workshop_pit_turnover_order
        set is_delete = 1
        where id = #{id,jdbcType=INTEGER}
    </update>

<!--    &lt;!&ndash;auto generated by MybatisCodeHelper on 2022-05-30&ndash;&gt;-->
<!--    <select id="getDataCount" resultType="java.lang.Integer">-->
<!--        select count(*)-->
<!--        FROM t_po_workshop_pit_turnover_order AS o-->
<!--        left join t_po_workshop_pit_turnover_order_plan op on o.id=op.turnover_order_id-->
<!--                     LEFT JOIN `equipment`.hv_bm_location AS l1 ON o.center_id = l1.id-->
<!--                     LEFT JOIN `equipment`.hv_bm_location AS l2 ON o.location_id = l2.id-->
<!--                     LEFT JOIN t_po_turn_over_pits_formula AS f ON f.id = o.formula_id-->
<!--                     LEFT JOIN `schedule`.hv_bm_crew AS c ON c.id = o.crew_id-->
<!--                     LEFT JOIN `schedule`.hv_bm_shift AS s-->
<!--                ON s.id = o.shift_id-->
<!--                     LEFT JOIN brewage_plan.t_pp_vinasse_source AS v ON v.id = o.vinasse_id-->
<!--                where is_delete = 0-->
<!--        <if test="dto.orderCode != null">-->
<!--            and (SELECT count(1)-->
<!--                 FROM t_po_workshop_pit_turnover_order_detail AS t-->
<!--                              LEFT JOIN t_po_workshop_pit_turnover_order_plan AS op ON op.id = t.first_pit_data_id-->
<!--                              LEFT JOIN t_po_workshop_pit_turnover_order_plan AS op1 ON op1.id = t.second_pit_data_id-->
<!--                              LEFT JOIN authority.sys_user AS u ON u.id = t.user_id-->
<!--                 WHERE t.is_delete = 0-->
<!--                   AND t.turnover_order_code = o.turnover_order_code-->
<!--                   and (op.pit_order_code like CONCAT('%', #{dto.orderCode}, '%') or-->
<!--                        op1.pit_order_code like CONCAT('%', #{dto.orderCode}, '%'))-->
<!--                   AND op.is_deleted = 0-->
<!--                   AND op1.is_deleted = 0) > 0-->
<!--        </if>-->
<!--        <if test="dto.centreId != null">-->
<!--            AND o.center_id = #{dto.centreId,jdbcType=INTEGER}-->
<!--        </if>-->

<!--        <if test="dto.locationId != null">-->
<!--            AND o.location_id = #{dto.locationId,jdbcType=INTEGER}-->
<!--        </if>-->

<!--        <if test="dto.pitCode != null">-->
<!--            AND op.pit_code =#{dto.pitCode}-->
<!--        </if>-->

<!--        <if test="dto.orderStatus != null">-->
<!--            AND o.order_status =#{dto.orderStatus}-->
<!--        </if>-->

<!--        <if test="dto.turnoverOrderCode != null">-->
<!--            AND o.turnover_order_code like concat('%', #{dto.turnoverOrderCode,jdbcType=VARCHAR}, '%')-->
<!--        </if>-->

<!--        <if test="dto.startTime != null and dto.endTime != null">-->
<!--            AND actual_endtime <![CDATA[>=]]> #{dto.startTime,jdbcType=TIMESTAMP}-->
<!--            AND actual_endtime <![CDATA[<=]]> #{dto.endTime,jdbcType=TIMESTAMP}-->
<!--        </if>-->
<!--    </select>-->

    <select id="getOrderByPage" resultMap="BaseResultMap">
        SELECT o.*,
        count(op.id) as orderPlanNum,
        su1.user_name as creator_name,
        su2.user_name as updater_name,
               l1.`name`                 AS centre_name,
               l2.`name`                 AS location_name,
               f.back_alcoholic_quantity AS past_back_alcoholic_quantity_single,
               f.qu_quantity             AS past_qu_quantity_single,
               c.crew_name,
               s.shift_name,
               v.code                    AS vinasse_name
        FROM t_po_workshop_pit_turnover_order AS o
        left join t_po_workshop_pit_turnover_order_plan op on o.id=op.turnover_order_id
        LEFT JOIN authority.sys_user su1 on o.creator_id=su1.id
        LEFT JOIN authority.sys_user su2 on o.updater_id=su2.id
                     LEFT JOIN `equipment`.hv_bm_location AS l1 ON o.center_id = l1.id
                     LEFT JOIN `equipment`.hv_bm_location AS l2 ON o.location_id = l2.id
                     LEFT JOIN t_po_turn_over_pits_formula AS f ON f.id = o.formula_id
                     LEFT JOIN `schedule`.hv_bm_crew AS c ON c.id = o.crew_id
                     LEFT JOIN `schedule`.hv_bm_shift AS s ON s.id = o.shift_id
                     LEFT JOIN brewage_plan.t_pp_vinasse_source AS v ON v.id = o.vinasse_id
                where o.is_delete = 0

        <if test="dto.orderCode != null">
            and (SELECT count(1)
                 FROM t_po_workshop_pit_turnover_order_detail AS t
                              LEFT JOIN t_po_workshop_pit_turnover_order_plan AS op ON op.id = t.first_pit_data_id
                              LEFT JOIN t_po_workshop_pit_turnover_order_plan AS op1 ON op1.id = t.second_pit_data_id
                              LEFT JOIN authority.sys_user AS u ON u.id = t.user_id
                 WHERE t.is_delete = 0
                   AND t.turnover_order_code = o.turnover_order_code
                   and (op.pit_order_code like CONCAT('%', #{dto.orderCode}, '%') or
                        op1.pit_order_code like CONCAT('%', #{dto.orderCode}, '%'))
                   AND op.is_deleted = 0
                   AND op1.is_deleted = 0) > 0
        </if>
        <if test="dto.centreId != null">
            AND o.center_id = #{dto.centreId,jdbcType=INTEGER}
        </if>

        <if test="dto.locationId != null">
            AND o.location_id = #{dto.locationId,jdbcType=INTEGER}
        </if>

        <if test="dto.turnoverOrderCode != null">
            AND o.turnover_order_code like concat('%', #{dto.turnoverOrderCode,jdbcType=VARCHAR}, '%')
        </if>

        <if test="dto.pitCode != null">
            AND op.pit_code =#{dto.pitCode}
        </if>

        <if test="dto.orderStatus != null and dto.orderStatus.size() != 0">
            AND o.order_status in
            <foreach collection="dto.orderStatus" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>

        <if test="dto.startTime != null and dto.endTime != null">
            AND actual_endtime <![CDATA[>=]]> #{dto.startTime,jdbcType=TIMESTAMP}
            AND actual_endtime <![CDATA[<=]]> #{dto.endTime,jdbcType=TIMESTAMP}
        </if>

        group by o.id
        order by o.turnover_order_code
    </select>

    <select id="selectPitOrderData" resultType="com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.ChoosePitOrderVO">
        SELECT po.id,
               po.order_code,
               po.center_id,
               po.location_id,
               po.pit_id,
               po.in_pit_date,
               wp.the_row,
               wp.the_column,
        <!--如果翻窖状态不为空则取翻窖状态-->
        case po.turnover_status is null
                when true then po.pit_status
                else po.turnover_status
                end AS pit_status,
        <!--如果翻窖甑口数不为空则取翻窖甑口数-->
        case po.turn_over_pit_num is null
                when true then po.in_pit_num
                else po.turn_over_pit_num
                end   AS in_pit_num,
        po.turn_over_pit_num,
        s.id          AS vinasse_id,
        s.`code`      AS vinasse_name,
        p.full_pit_id AS pit_code,
        wp.area,
        po.pit_status,
        CASE po.out_pit_finish_time IS NULL
                -- 发酵状态：发酵天数=当前时间-封窖完成时间
                        -- 开窖状态：发酵天数=起窖时间-封窖完成时间
                WHEN TRUE THEN
                        DATEDIFF(NOW(), po.seal_confirm_time)
                ELSE
                        DATEDIFF(po.out_pit_finish_time, po.seal_confirm_time)
                END   AS fermentation_day
                FROM t_po_workshop_pit_order AS po
                             LEFT JOIN brewage_plan.t_pp_vinasse_source AS s ON po.vinasse_id = s.id
                             LEFT JOIN t_po_workshop_full_pit AS p ON p.id = po.pit_id
                             LEFT JOIN t_po_workshop_pit AS wp ON wp.pit_code = p.pid_fir
                where po.is_deleted = 0
                  AND wp.is_deleted = 0
                  AND p.is_deleted = 0
        <if test="dto.isLaterIncrease != true">
            AND po.is_current = 1
        </if>

        <if test="dto.centreId != null">
            AND po.center_id = #{dto.centreId,jdbcType=INTEGER}
        </if>

        <if test="dto.locationId != null">
            AND po.location_id = #{dto.locationId,jdbcType=INTEGER}
        </if>
        <if test="dto.noDz != null and dto.noDz == true">
            AND s.code != "DZ"
        </if>

        <if test="(isAllQuery == null or isAllQuery == false) and dto.isLaterIncrease != true">
            AND (po.pit_status = 1 or po.pit_status = 3)
            <!--     需要把导入甑口数相加       -->
            <!--            AND po.in_pit_num is not null-->
        </if>

        <if test="dto.pitCode != null">
            AND p.full_pit_id like concat('%'
                    , #{dto.pitCode,jdbcType=VARCHAR}
                    , '%')
        </if>

        <if test="dto.fermentationDay != null">
            AND DATEDIFF(NOW(), po.seal_confirm_time) <![CDATA[>=]]> #{dto.fermentationDay,jdbcType=INTEGER}
        </if>

        <if test="dto.vinasseId != null">
            AND (po.vinasse_id = #{dto.vinasseId,jdbcType=INTEGER} or po.vinasse_id is null)
        </if>

        <if test="dto.area != null">
            AND wp.area like concat('%'
                    , #{dto.area,jdbcType=VARCHAR}
                    , '%')
        </if>


        order by p.full_pit_id
    </select>

    <select id="selectExcInputNum" resultType="com.hvisions.brewage.mkwine.vo.TurnOverPitsManage.ChoosePitOrderVO">
        SELECT order_code_id AS id, SUM(in_pot_num_exc) AS in_pit_num
        FROM t_po_workshop_pit_order_sap
                WHERE is_deleted = 0
                  AND order_code_id IN
        <foreach collection="list" index="index" item="item" open="(" separator=", " close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        GROUP BY order_code_id
    </select>

    <update id="updatePitOrder">
        update t_po_workshop_pit_order
        <trim prefix="set" suffixOverrides=",">
            <trim prefix=" turn_over_pit_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.pitOrderId,jdbcType=INTEGER} then #{item.turnoverPotNum,jdbcType=INTEGER}
                </foreach>
            </trim>
            turnover_status = 7,
            order_status = 1,
            <if test="needChange != null and needChange == true and vinasseId != null">
                vinasse_id = #{vinasseId,jdbcType=INTEGER},
            </if>
            turn_over_finish_time = NOW()
        </trim>
        where is_deleted = 0
          AND id IN
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.pitOrderId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="getOutOrderIdByInOrderId" resultType="java.lang.Integer">
        SELECT p1.pit_order_id
        FROM t_po_workshop_pit_turnover_order_detail AS d
                     LEFT JOIN t_po_workshop_pit_turnover_order_plan AS p ON p.id = d.second_pit_data_id
                     LEFT JOIN t_po_workshop_pit_turnover_order_plan AS p1 ON p1.id = d.first_pit_data_id
        WHERE p.pit_order_id = #{inOrderId}
          AND p.is_deleted = 0
          AND p1.is_deleted = 0
          AND d.is_delete = 0
    </select>
    <select id="selectLocationQuBatchList"
            resultType="com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.UseMaterialQueryBatchDTO">
        SELECT id.id, id.id dataId, o.demand_order taskNo, id.remain_quantity, iod.batch,v.license_plate_number, io.material_id, io.material_code, io.material_name
        FROM t_wp_item_detail id
        LEFT JOIN t_wp_issue_order_detail iod on id.issue_detail_id = iod.id
        LEFT JOIN t_wp_issue_order_item io on io.id = iod.item_id
        LEFT JOIN vehicle_transport v ON v.id = iod.vehicle_transport_id
        LEFT JOIN t_wp_issue_order o on o.id = io.order_id
        where id.deleted = 0
            and id.accept_center_id = #{centerId}
            and id.accept_location_id = #{locationId}
            and io.material_code = #{materialCode}
            <if test="dataId != null">
                and id.id = #{dataId}
            </if>
            <if test="lineName != null">
                and id.accept_line = #{lineName}
            </if>
            <if test="lastBatch != null">
                and iod.batch = #{lastBatch}
            </if>
            <if test="inStock != null and inStock == true">
                and id.remain_quantity > 0
            </if>
    </select>
    <select id="selectLocationGLBatchList"
            resultType="com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.UseMaterialQueryBatchDTO">
        SELECT od.id, od.material_id, od.material_code, od.material_name, od.flow_batch batch, o.order_no taskNo
        FROM brewage_rawmaterial_production.t_mpd_sd_order_detail od
         join brewage_rawmaterial_production.t_mpd_sorghum_dispense_order o on o.id = od.order_id
        where od.deleted = 0 and o.deleted = 0
        where od.deleted = 0 and o.deleted = 0
          and o.center_id = #{centerId}
          and od.material_code = #{materialCode}
        order by od.id desc
            limit 1
    </select>
    <select id="selectLocationDKBatchList"
            resultType="com.hvisions.brewage.mkwine.dto.TurnOverPitsManage.UseMaterialQueryBatchDTO">
        SELECT od.id, od.material_id, od.material_code, od.material_name, od.flow_batch batch, o.order_no taskNo
        FROM brewage_rawmaterial_production.t_mpd_bi_order_detail od
         join brewage_rawmaterial_production.t_mpd_bran_issue_order o on o.id = od.order_id
        where od.deleted = 0 and o.deleted = 0
          and o.center_id = #{centerId}
          and od.material_code = #{materialCode}
        order by od.id desc
            limit 1
    </select>

    <update id="updateQuRemainQuantity">
        UPDATE `t_wp_item_detail`
        SET remain_quantity = #{remainQuantity}
        where id = #{id}
    </update>
    <update id="updateQuantity">
        UPDATE `t_po_workshop_pit_turnover_order` t1
        SET t1.qu_quantity             = (SELECT SUM(qu_quantity)
                                          FROM t_po_workshop_pit_turnover_order_detail
                                          WHERE turnover_order_code = t1.turnover_order_code
                                            AND is_delete = FALSE),
            t1.back_alcoholic_quantity = (SELECT SUM(back_alcoholic_quantity)
                                          FROM t_po_workshop_pit_turnover_order_detail
                                          WHERE turnover_order_code = t1.turnover_order_code
                                            AND is_delete = FALSE)
                where t1.id in
        <foreach collection="ids" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </update>

    <select id="getPitTurnoverOrder" resultType="com.hvisions.brewage.vo.tpo.TPoWorkshopPitTurnoverOrderVO">
        SELECT
            o.turnover_order_code as taskNo,
            o.center_id,
            o.location_id,
            CASE o.order_status WHEN 0 THEN '待执行' WHEN 1 THEN '执行中' WHEN 2 THEN '已完成' WHEN 3 THEN '已审核' ELSE '待执行' END `status`,
            o.actual_starttime as startTime,
            o.actual_endtime as endTime,
            o.id,
            o.create_time,
            o.update_time,
            o.creator_id,
            o.updater_id,
            su1.user_name as creatorName,
            su2.user_name as updaterName
        FROM
            t_po_workshop_pit_turnover_order AS o
                LEFT JOIN t_po_workshop_pit_turnover_order_plan op ON o.id = op.turnover_order_id
                LEFT JOIN authority.sys_user su1 ON o.creator_id = su1.id
                LEFT JOIN authority.sys_user su2 ON o.updater_id = su2.id
        WHERE
            o.is_delete = 0 and op.pit_order_code=#{query.pitOrder,jdbcType=VARCHAR}
        GROUP BY
            o.id
        ORDER BY
            o.create_time
    </select>

    <select id="selectFJMaterialCode" resultType="com.hvisions.brewage.vo.tpo.FJMaterialCodeVO">
        SELECT
            c.material_code as qfMaterialCode,
            d.material_code as hjMaterialCode
        FROM
            t_po_turn_over_pits_formula a
                INNER JOIN brewage_plan.t_pp_vinasse_source b ON a.out_pit_source = b.`code`
                INNER JOIN materials.hv_bm_material c ON a.qu_code = c.id
                INNER JOIN materials.hv_bm_material d ON a.back_alcoholic_code = d.id
        WHERE
            b.id = #{vinasseId,jdbcType=INTEGER} 	AND a.in_pit_source = #{updateVinasseName,jdbcType=VARCHAR} AND a.turnover_times =#{turnoverTimes,jdbcType=INTEGER} and a.is_deleted=0
        GROUP BY a.id
        LIMIT 1
    </select>
</mapper>