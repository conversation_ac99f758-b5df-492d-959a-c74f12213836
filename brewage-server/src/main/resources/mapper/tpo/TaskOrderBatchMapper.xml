<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.brewage.dao.tpo.TaskOrderBatchMapper">
  <resultMap id="BaseResultMap" type="com.hvisions.brewage.entity.tpo.TaskOrderBatch">
    <!--@Table t_po_task_order_batch-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="task_no" jdbcType="VARCHAR" property="taskNo" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="batch_task_no" jdbcType="VARCHAR" property="batchTaskNo" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="pit_order" jdbcType="VARCHAR" property="pitOrder" />
    <result column="use_count" jdbcType="DECIMAL" property="useCount" />
    <result column="require_equipment" jdbcType="VARCHAR" property="requireEquipment" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_id" jdbcType="INTEGER" property="materialId" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="use_id" jdbcType="INTEGER" property="useId" />
    <result column="use_time" jdbcType="TIMESTAMP" property="useTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="updater_id" jdbcType="INTEGER" property="updaterId" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="task_material_type" jdbcType="VARCHAR" property="taskMaterialType" />
    <result column="turnover_pot_num" jdbcType="INTEGER" property="turnoverPotNum" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="input_type" jdbcType="CHAR" property="inputType" />
    <result column="data_id" jdbcType="INTEGER" property="dataId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, task_no, task_name, batch_task_no, batch, pit_order, use_count, require_equipment, 
    material_code, material_id, material_name, use_id, use_time, create_time, update_time, 
    creator_id, updater_id, deleted, task_material_type, turnover_pot_num, task_id, input_type, 
    data_id
  </sql>

    <select id="selectListByOrder" resultType="com.hvisions.brewage.vo.tpo.TaskOrderBatchVO">
      select ob.batch, ob.task_no, ob.pit_order, ob.material_id, ob.material_code, ob.material_name
           , sum(ob.use_count) useCount, ob.require_equipment, ob .create_time, ob.task_name, ob.task_material_type
      from t_po_task_order_batch ob
       join t_po_workshop_pit_order o on o.order_code = ob.pit_order and o.is_deleted = 0
      where deleted = 0 and o.id = #{orderCodeId}
      <if test="executionStatus != null">
        and ob.task_name = #{executionStatus}
      </if>
      GROUP BY ob.pit_order, ob.material_id, ob.material_code, ob.material_name, ob.task_name
    </select>
    <select id="selectOrderUseByType" resultType="com.hvisions.brewage.mkwine.vo.ProductionProcess.WorkshopPitOrderSap.BatchReturnTopVO">
      SELECT material_id, material_code, material_name, sum(use_count) quality
      FROM `t_po_task_order_batch` ob where ob.deleted = 0
        and ob.pit_order in
        <foreach close=")" collection="orderCodes" item="i" open="(" separator=",">
          #{i}
        </foreach>
        and ob.input_type = #{inputType}
      GROUP BY material_code
    </select>
</mapper>