package com.hvisions.powder.product.entity.exception;

import com.hvisions.powder.entity.SysBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

@Table(name ="t_mkp_system_join_log")
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
public class TMkpSystemJoinLog extends SysBase implements Serializable {

    /**
     * 系统类型
     */
    private String systemType;

    /**
     * 返回结果
     */
    private String reqResult;

    /**
     * 连接耗时 毫秒
     */
    private Long consumeTime;

    /**
     * 涉及的业务模块
     */
    private String businessModulesInvolved;

}