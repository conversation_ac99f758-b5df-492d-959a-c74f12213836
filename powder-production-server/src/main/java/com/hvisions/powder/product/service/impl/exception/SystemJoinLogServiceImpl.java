package com.hvisions.powder.product.service.impl.exception;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.powder.configuration.ThreadPoolExecutorFactory;
import com.hvisions.powder.dto.product.exception.SystemJoinLogQueryDTO;
import com.hvisions.powder.enums.BusinessModulesInvolvedEnum;
import com.hvisions.powder.product.entity.exception.TMkpSystemJoinLog;
import com.hvisions.powder.product.mapper.exception.SystemJoinLogMapper;
import com.hvisions.powder.product.service.exception.SystemJoinLogService;
import com.hvisions.powder.sap.constant.LinkConfig;
import com.hvisions.powder.sap.dto.PIDataResponseDto;
import com.hvisions.powder.sap.dto.SapBaseResponseDto;
import com.hvisions.powder.sap.dto.wincos.WincosBaseDataDto;
import com.hvisions.powder.sap.dto.wincos.WincosBaseResponseDto;
import com.hvisions.powder.sap.dto.wms.WmsBaseResponseDto;
import com.hvisions.powder.sap.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.Socket;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service
public class SystemJoinLogServiceImpl extends ServiceImpl<SystemJoinLogMapper, TMkpSystemJoinLog> implements SystemJoinLogService {

    @Resource
    private SapService sapService;

    @Resource
    private WincosService wincosService;

    @Resource
    private WmsService wmsService;

    @Resource
    private ZonghengService zonghengService;

    @Resource
    private ReqService reqService;

    @Autowired
    private LinkConfig linkConfig;

    @Override
    public Page<TMkpSystemJoinLog> getPageList(SystemJoinLogQueryDTO pageInfo) {
        return PageHelperUtil.getPage(baseMapper::getPageList, pageInfo, TMkpSystemJoinLog.class);
    }

    @Override
    public List<TMkpSystemJoinLog> check() {
        List<TMkpSystemJoinLog> list = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch countDownLatch = new CountDownLatch(11);
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //威斯派克调用
            TMkpSystemJoinLog joinLog = linkToVspec();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //sap调用
            TMkpSystemJoinLog joinLog = linkToSap();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //调用PI
            TMkpSystemJoinLog joinLog = linkToPi();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //Wincos调用
            TMkpSystemJoinLog joinLog = linkToWincos();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //wms调用
            TMkpSystemJoinLog joinLog = linkToWms();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //纵横调用
            TMkpSystemJoinLog joinLog = linkToZongheng();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //地磅调用
            TMkpSystemJoinLog joinLog = linkToAvs();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //安消一体调用
            TMkpSystemJoinLog joinLog = linkArt();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //酿酒8中心MES调用
            TMkpSystemJoinLog joinLog = linkToLuohan();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //曲房温湿度调用
            TMkpSystemJoinLog joinLog = linkToQufang();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        ThreadPoolExecutorFactory.getInstance().execute(() -> {
            //TMS调用
            TMkpSystemJoinLog joinLog = linkToTms();
            list.add(joinLog);
            countDownLatch.countDown();
        });
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error("计数器统计出错" + e.getMessage());
        }
        TMkpSystemJoinLog add = new TMkpSystemJoinLog();
        add.setSystemType("ESB服务");
        add.setReqResult("连接异常");
        //所有系统校验结束后验证是否有成功的，如果有走ESB的系统成功则说明ESB通讯没问题
        for (TMkpSystemJoinLog joinLog : list) {
            if ("连接正常".equals(joinLog.getReqResult()) && !"压曲码垛控制".equals(joinLog.getSystemType()) && !"安消一体系统".equals(joinLog.getSystemType())) {
                add.setReqResult("连接正常");
                add.setConsumeTime(100L);
                break;
            }
        }
        baseMapper.insert(add);
        list.add(add);
        return list;
    }

    /**
     * 调用TMS
     *
     */
    private TMkpSystemJoinLog linkToTms() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("曲斗运输管理系统");
        joinLog.setReqResult("连接正常");
        try {
            reqService.getTms();
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToTms.getCode());
            log.error("连曲斗运输管理系统失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    @Override
    public TMkpSystemJoinLog checkByType(String type) {
        TMkpSystemJoinLog joinLog = null;
        switch (type){
            case "曲房温湿度":
                joinLog = linkToQufang();
                break;
            case "酿酒8中心MES":
                joinLog = linkToLuohan();
                break;
            case "地磅系统":
                joinLog = linkToAvs();
                break;
            case "安消一体系统":
                joinLog = linkArt();
                break;
            case "压曲码垛控制":
                joinLog = linkToZongheng();
                break;
            case "库房管理":
                joinLog = linkToWms();
                break;
            case "小麦预处理":
                joinLog = linkToWincos();
                break;
            case "SAP系统":
                joinLog = linkToSap();
                break;
            case "威斯派克系统":
                joinLog = linkToVspec();
                break;
            case "PI实时数据库":
                joinLog = linkToPi();
                break;
            case "曲斗运输管理系统":
                joinLog = linkToTms();
                break;
            case "ESB服务":
                //ESB没有服务调用
                break;
            default:
                throw new BaseKnownException(555, "传入的系统类型有误！");
        }
        return joinLog;
    }

    /**
     * 调用曲房温湿度信息
     *
     */
    private TMkpSystemJoinLog linkToQufang() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("曲房温湿度");
        joinLog.setReqResult("连接正常");
        try {
            reqService.getQufang();
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToQufang.getCode());
            log.error("连接曲房温湿度失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用8中心信息
     *
     */
    private TMkpSystemJoinLog linkToLuohan() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("酿酒8中心MES");
        joinLog.setReqResult("连接正常");
        try {
            reqService.getLuohan();
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToLuohan.getCode());
            log.error("连接酿酒8中心MES失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用安消一体系统
     *
     */
    private TMkpSystemJoinLog linkArt() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("安消一体系统");
        joinLog.setReqResult("连接正常");
        try {
            reqService.getArt();
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkArt.getCode());
            log.error("连接安消一体系统失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用地磅信息
     *
     */
    private TMkpSystemJoinLog linkToAvs() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("地磅系统");
        joinLog.setReqResult("连接正常");
        try {
            reqService.getAvs();
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToAvs.getCode());
            log.error("连接地磅系统失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用纵横信息
     *
     */
    private TMkpSystemJoinLog linkToZongheng() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("压曲码垛控制");
        joinLog.setReqResult("连接正常");
        try {
            zonghengService.cleanLineData(linkConfig.getZongheng());
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToZongheng.getCode());
            log.error("连接压曲码垛控制失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用WMS信息
     *
     */
    private TMkpSystemJoinLog linkToWms() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("库房管理");
        joinLog.setReqResult("连接正常");
        try {
            WmsBaseResponseDto link = wmsService.link("111");
            log.info(JSONObject.toJSONString(link));
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToWms.getCode());
            log.error("连接库房管理失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用布勒信息
     *
     */
    private TMkpSystemJoinLog linkToWincos() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("小麦预处理");
        joinLog.setReqResult("连接正常");
        try {
            WincosBaseDataDto wincosBaseDataDto = new WincosBaseDataDto();
            wincosBaseDataDto.setBillNo("111");
            WincosBaseResponseDto wincosBaseResponseDto = wincosService.endMillingTask(wincosBaseDataDto);
            log.info(JSONObject.toJSONString(wincosBaseResponseDto));
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToWincos.getCode());
            log.error("连接小麦预处理失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }


    /**
     * 调用PI信息
     *
     */
    private TMkpSystemJoinLog linkToPi() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("PI实时数据库");
        joinLog.setReqResult("连接正常");
        try {
            List<PIDataResponseDto> piDataResponseDtos = reqService.pullPIData();
            log.info(JSONArray.toJSONString(piDataResponseDtos));
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToPi.getCode());
            log.error("连接PI实时数据库失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用sap信息
     *
     */
    private TMkpSystemJoinLog linkToSap() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("SAP系统");
        joinLog.setReqResult("连接正常");
        try {
            List<String> materialCodes = Arrays.asList("34000077", "34000076", "34000081", "34000080", "34000074", "34000073");
            SapBaseResponseDto sapBaseResponseDto = sapService.getInventoryByStorageNo(materialCodes, "1397");
            log.info(JSONObject.toJSONString(sapBaseResponseDto));
        } catch (Exception e) {
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToSap.getCode());
            log.error("连接SAP系统失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

    /**
     * 调用威斯派克信息
     *
     */
    private TMkpSystemJoinLog linkToVspec() {
        long start = Instant.now().toEpochMilli();
        TMkpSystemJoinLog joinLog = new TMkpSystemJoinLog();
        joinLog.setSystemType("威斯派克系统");
        joinLog.setReqResult("连接正常");
        String ip = "***********"; // 替换为你要检查的IP地址
        int port = 8322; // 替换为你要检查的端口号

        try (Socket socket = new Socket(ip, port)) {
            // 如果能够创建并打开Socket，则通信正常
            //return true;
            log.info(JSONObject.toJSONString("威斯派克系统连接正常"));
        } catch (Exception e) {
            // 如果抛出异常，则通信异常
            joinLog.setReqResult("连接异常");
            joinLog.setBusinessModulesInvolved(BusinessModulesInvolvedEnum.linkToVspec.getCode());
            log.error("连接SAP系统失败:" + e.getMessage());
        }
        long end = Instant.now().toEpochMilli();
        joinLog.setConsumeTime(end - start);
        baseMapper.insert(joinLog);
        return joinLog;
    }

}
