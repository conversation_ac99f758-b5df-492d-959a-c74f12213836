package com.hvisions.purchase.dto.purchase.daily.delivery.detail;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName: DzSunReportDetailDTO
 * @description:
 * @date 2025/4/1 9:30
 */

@Data
public class DzSunReportDetailDTO {

    @ApiModelProperty(value = "出厂时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "出厂数量")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "运输信息")
    private String licensePlateNumber;

    @ApiModelProperty(value = "接收单位")
    private String receiptPlace;

    @ApiModelProperty(value = "代码")
    private String receiptPlaceCode;
}
