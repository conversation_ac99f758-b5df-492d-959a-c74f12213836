package com.hvisions.purchase.dto.purchase.demand.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "要货需求单分页返回dto")
public class DeliveryInspectPageDTO {

    @ApiModelProperty(value = "id")
    protected Integer id;

    @ApiModelProperty(value = "创建时间")
    protected Date createTime;

    @ApiModelProperty(value = "送货车辆id")
    private Integer deliveryDetailId;

    @ApiModelProperty(value = "检验状态：0-未检验；1-已检验")
    private String inspectState;

    @ApiModelProperty(value = "检验结果：合格/不合格")
    private String inspectResult;

    @ApiModelProperty(value = "不合格原因")
    private String inspectFailReason;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片地址")
    private String picUrls;

    @ApiModelProperty(value = "审批状态：0-未审批；1-已审批")
    private String auditState;

    @ApiModelProperty(value = "提交人")
    private Integer submitId;

    @ApiModelProperty(value = "提交用户")
    private String submitUser;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "审核人")
    private Integer auditId;

    @ApiModelProperty(value = "审核用户")
    private String auditUser;

    @ApiModelProperty(value = "审核时间")
    private Date auditTime;

    @ApiModelProperty(value = "审核结果：同意/不同意")
    private String auditResult;

    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value = "是否补货物：0-否；1-是")
    private String isSupplement;

    @ApiModelProperty(value = "补货时间")
    private Date supplementTime;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料Code")
    private String materialCode;

    @ApiModelProperty(value = "预计到货重量")
    private BigDecimal srmEstimateWeight;

    @ApiModelProperty(value = "产地")
    private String srmPlaceProduction;

    @ApiModelProperty(value = "品种")
    private String srmVariety;

    @ApiModelProperty(value = "批次")
    private String srmBatch;
}
