package com.hvisions.purchase.dto.purchase.demand.order;

import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "感官检验分页条件dto")
public class DeliveryInspectQueryDTO extends PageInfo {

    @ApiModelProperty(value = "检验状态：0-未检验；1-已检验")
    private String inspectState;

    @ApiModelProperty(value = "审批状态：0-未审批；1-已审批")
    private String auditState;
}
