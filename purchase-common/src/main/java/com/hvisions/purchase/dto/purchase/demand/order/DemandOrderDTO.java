package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 要货需求单新增修改dto
 * @author: yyy
 * @time: 2022/4/6 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "要货需求单新增修改dto")
public class DemandOrderDTO extends SysBaseDTO {

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "要货日期不能为空")
    private Date demandDate;

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    @ApiModelProperty(value = "要货数量")
    @NotNull(message = "要货数量不能为空")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "入库地点id")
    @NotNull(message = "入库地点id不能为空")
    private Integer locationId;

    @ApiModelProperty(value = "修改备注")
    private String updateRemark;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "基地 黄舣/制曲/罗汉/小市/皂角巷/国窖")
    private String baseName;
}
