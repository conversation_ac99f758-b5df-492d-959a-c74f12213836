package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "退杂申请dto")
public class ImpurityAuditDTO {

    @ApiModelProperty(value = "日送货计划子项id")
    private Integer deliveryItemId;

    @ApiModelProperty(value = "退杂类型 1-固定位收货退杂；2-二期转一次退杂")
    private String impurityType;

    @ApiModelProperty(value = "是否补货物：0-否；1-是")
    private String isSupplement;

    @ApiModelProperty(value = "补货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date supplementTime;
}
