package com.hvisions.purchase.dto.purchase.demand.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料需求统计DTO
 * @author: Jcao
 * @time: 2022/6/15 14:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料需求统计DTO")
public class MaterialDemandCensusDTO {

    @ApiModelProperty(value = "高粱需求数量")
    private BigDecimal glDemandQuantity;

    @ApiModelProperty(value = "高粱执行中，未送货数量")
    private BigDecimal glPerformQty;

    @ApiModelProperty(value = "高粱待执行，未送货数量")
    private BigDecimal glWaitQty;

    @ApiModelProperty(value = "高粱库存数量")
    private BigDecimal glStockQuantity;

    @ApiModelProperty(value = "稻壳需求数量")
    private BigDecimal dkDemandQuantity;

    @ApiModelProperty(value = "稻壳执行中，未送货数量")
    private BigDecimal dkPerformQty;

    @ApiModelProperty(value = "稻壳待执行，未送货数量")
    private BigDecimal dkWaitQty;

    @ApiModelProperty(value = "稻壳库存数量")
    private BigDecimal dkStockQuantity;

}
