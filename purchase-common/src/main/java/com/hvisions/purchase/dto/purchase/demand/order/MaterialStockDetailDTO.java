package com.hvisions.purchase.dto.purchase.demand.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料库存详情dto
 * @author: yyy
 * @time: 2022/7/14 14:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料库存详情dto")
public class MaterialStockDetailDTO {

    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    @ApiModelProperty(value = "gl一期库存")
    private BigDecimal gl1;

    @ApiModelProperty(value = "gl二期库存")
    private BigDecimal gl2;

    @ApiModelProperty(value = "dk一期库存")
    private BigDecimal dk1;

    @ApiModelProperty(value = "dk二期库存")
    private BigDecimal dk2;

    @ApiModelProperty(value = "采购计划剩余数量")
    private BigDecimal planRemainTotal;

    @ApiModelProperty(value = "今日未送达数量")
    private BigDecimal undeliveredTotal;


}
