package com.hvisions.purchase.dto.purchase.demand.order;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "月需求明细查询列表")
public class MonthDemandDetailDTO extends SysBaseDTO {

    @ApiModelProperty(value = "月需求单id")
    private Integer mouthDemandId;

    @ApiModelProperty(value = "采购订单id")
    private Integer purchaseOrderId;

    @ApiModelProperty(value = "采购订单编号")
    private String sapOrder;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "供货数量kg")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "品种")
    private String variety;

    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "已到货重量kg")
    private BigDecimal arrivalWeight;
}
