package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import com.hvisions.purchase.dto.purchase.order.PurchaseOrderMonthDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "月需求查询列表")
public class MonthDemandOrderDTO extends SysBaseDTO {

    @ApiModelProperty(value = "要货需求单号")
    private String orderNo;

    @ApiModelProperty(value = "类型：1-月要货需求、2-季度要货需求")
    private String type;

    @ApiModelProperty(value = "物料代码")
    private String materialCode;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "要货数量kg")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "预送数量")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal harvestQuantity;

    @ApiModelProperty(value = "要货需求状态;0-待下发、0-待执行、1-执行中、2-已完成")
    private String state;

    @ApiModelProperty(value = "入库地点id")
    private Integer locationId;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "修改备注")
    private String updateRemark;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "采购单月需求子集查询列表")
    List<MonthDemandDetailDTO> demandDetailDTOList;
}
