package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "月要货需求单分页条件dto")
public class MonthDemandQueryDTO extends PageInfo {

    @ApiModelProperty(value = "要货需求单号")
    private String orderNo;

    @ApiModelProperty(value = "要货需求id")
    private Integer id;

    @ApiModelProperty(value = "要货需求状态;0-待下发、0-待执行、1-执行中、2-已完成")
    private String state;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "要货周期")
    private String yearMonthStr;

    @ApiModelProperty(value = "基地 黄舣/罗汉/非遗")
    private String baseName;
}
