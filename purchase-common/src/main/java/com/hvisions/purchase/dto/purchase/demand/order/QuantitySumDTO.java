package com.hvisions.purchase.dto.purchase.demand.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 物料未送货数量、库存，数量总和DTO
 * @author: Jcao
 * @time: 2022/6/15 14:00
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "物料未送货数量、库存，数量总和DTO")
public class QuantitySumDTO {

    @ApiModelProperty(value = "执行中，未送货数量")
    private BigDecimal performQty;

    @ApiModelProperty(value = "待执行，未送货数量")
    private BigDecimal waitQty;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockQuantity;

}
