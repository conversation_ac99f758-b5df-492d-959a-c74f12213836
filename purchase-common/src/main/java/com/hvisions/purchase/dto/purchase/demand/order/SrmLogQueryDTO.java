package com.hvisions.purchase.dto.purchase.demand.order;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "srm系统交互日志分页条件dto")
public class SrmLogQueryDTO extends PageInfo {

    @ApiModelProperty(value = "业务类型 年度采购需求/月度要货需求/日要货需求/车辆数据同步/退杂申请/过账数据同步")
    private String businessType;

    @ApiModelProperty(value = "请求状态 成功/失败")
    private String postStatus;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "交互类型 发送/接收")
    private String interactionType;
}
