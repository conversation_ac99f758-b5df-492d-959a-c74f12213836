package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(description = "提交感官审核结果dto")
public class SubmitAuditResultDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "审核结果：同意/不同意")
    private String auditResult;

    @ApiModelProperty(value = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value = "是否补货物：0-否；1-是")
    private String isSupplement;

    @ApiModelProperty(value = "补货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date supplementTime;
}
