package com.hvisions.purchase.dto.purchase.demand.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "提交感官检验结果dto")
public class SubmitInspectResultDTO {

    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "检验结果：合格/不合格")
    private String inspectResult;

    @ApiModelProperty(value = "不合格原因")
    private String inspectFailReason;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "图片地址")
    private String picUrls;

    @ApiModelProperty(value = "审核人")
    private Integer auditId;

    @ApiModelProperty(value = "审核用户")
    private String auditUser;
}
