package com.hvisions.purchase.dto.purchase.demand.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(description = "供应商场内管理分页返回dto")
public class VendorManagePageDTO {

    @ApiModelProperty(value = "id")
    protected Integer id;

    @ApiModelProperty(value = "创建时间")
    protected Date createTime;

    @ApiModelProperty(value = "送货车辆id")
    private Integer deliveryDetailId;

    @ApiModelProperty(value = "送货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryTime;

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "问题描述")
    private String questionDescription;

    @ApiModelProperty(value = "图片")
    private String picUrls;

    @ApiModelProperty(value = "状态 1-待处理：2-已处理；")
    private String state;

    @ApiModelProperty(value = "处理详情")
    private String disposeDescription;

    @ApiModelProperty(value = "感官检验结果")
    private String inspectResult;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "检验单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "入场时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date admissionTime;

    @ApiModelProperty(value = "srm品种")
    private String srmVariety;

    @ApiModelProperty(value = "srm产地")
    private String srmPlaceProduction;

    @ApiModelProperty(value = "srm批次")
    private String srmBatch;

    @ApiModelProperty(value = "预计送货数量")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "处理图片")
    private String disposePicUrls;

    @ApiModelProperty(value = "处理用户")
    private String disposeUser;

    @ApiModelProperty(value = "提交用户")
    private String submitUser;

    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date submitTime;
}
