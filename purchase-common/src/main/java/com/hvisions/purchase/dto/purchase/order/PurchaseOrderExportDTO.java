package com.hvisions.purchase.dto.purchase.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @author: yyy
 * @time: 2022/8/31 13:47
 */

@Data
@EqualsAndHashCode
@ApiModel(description = "采购单导入DTO")
public class PurchaseOrderExportDTO {


    @ApiModelProperty(value = "采购组编码")
    private String groupCode;

    @ApiModelProperty(value = "采购组织编码")
    private String organizationCode;

    @ApiModelProperty(value = "公司工厂代码编码")
    private String maintenanceCode;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    /**
     * 采购单详情
     */

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "物料单位")
    private String uom;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

}
