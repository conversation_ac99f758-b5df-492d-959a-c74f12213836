package com.hvisions.purchase.dto.purchase.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购单月需求子集查询列表")
public class PurchaseOrderMonthDTO extends SysBaseDTO {

    @ApiModelProperty(value = "供应商id")
    private Integer vendorId;

    @ApiModelProperty(value = "供应商Code")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private Integer vendorName;

    @ApiModelProperty(value = "采购重量")
    private BigDecimal purchaseQuantity;

    @ApiModelProperty(value = "已到货重量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "线下采购时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offlinePurchaseTime;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
