package com.hvisions.purchase.dto.purchase.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 采购单分页返回dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "采购单分页返回dto")
public class PurchaseOrderPageDTO {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "采购计划id")
    private Integer planId;

    @ApiModelProperty(value = "采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "SAP过账状态;已过账-0、未过账-1")
    private String sapState;

    @ApiModelProperty(value = "采购订单状态;执行中-0、待审批-1，执行中-2、已关闭-3、已删除-4")
    private String state;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "采购组织名称")
    private String organizationName;

    @ApiModelProperty(value = "采购组名称")
    private String groupName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "采购计划编码")
    private String planCode;

    @ApiModelProperty(value = "周期开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty(value = "周期结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "采购计划数量")
    private BigDecimal procurementNumber;

    @ApiModelProperty(value = "采购计划下其他采购单总数")
    private BigDecimal planOtherOrderQuantity;

    @ApiModelProperty(value = "已到货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "入库重量")
    private BigDecimal inboundWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "创建人")
    private String userName;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "SAP同步结果：0-同步成功，1-同步失败")
    private String sapResult;

    @ApiModelProperty(value = "预计送货数量")
    private BigDecimal deliverQuantity;

    @ApiModelProperty(value = "产地")
    private String productionPlace;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "车辆总数")
    private Integer total;

    @ApiModelProperty(value = "已关联数量")
    private BigDecimal totalDemandQuantity;

    @ApiModelProperty(value = "线下采购时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offlinePurchaseTime;

}
