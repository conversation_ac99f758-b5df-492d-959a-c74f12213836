package com.hvisions.purchase.dto.purchase.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * @Description: 采购单分页条件dto
 * @author: yyy
 * @time: 2022/3/10 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购单分页条件dto")
public class PurchaseOrderPageQueryDTO extends PageInfo {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料id")
    private String materialId;

    @ApiModelProperty(value = "采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "供应商ID")
    private Integer vendorId;

    @ApiModelProperty(value = "采购订单状态;执行中-0、待审批-1，执行中-2、已关闭-3、已删除-4")
    private List<String> states;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "同步结果")
    private String sapResult;

    @ApiModelProperty(value = "选中id集合")
    private List<Integer> ids;

    @ApiModelProperty(value = "需求单id")
    private Integer demandId;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "SAP过账状态;已过账-0、未过账-1")
    private String sapState;

}
