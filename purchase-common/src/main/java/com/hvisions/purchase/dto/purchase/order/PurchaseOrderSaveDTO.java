package com.hvisions.purchase.dto.purchase.order;

import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 采购单新增or修改
 * @author: Jcao
 * @time: 2022/4/7 14:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购单新增")
public class PurchaseOrderSaveDTO extends SysBaseDTO {

    @ApiModelProperty(value = "计划id")
    @NotNull(message = "计划id不能为空！")
    private Integer planningId;

    @ApiModelProperty(value = "公司工厂代码id")
    @NotNull(message = "公司工厂代码id不能为空！")
    private Integer maintenanceId;

    @ApiModelProperty(value = "采购组id")
    @NotNull(message = "采购组id不能为空！")
    private Integer groupId;

    @ApiModelProperty(value = "采购组织id")
    @NotNull(message = "采购组织id不能为空！")
    private Integer organizationId;

    @ApiModelProperty(value = "单位")
    private String uom;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "删除备注")
    private String deletedRemark;

    @ApiModelProperty(value = "采购单集合")
    List<PurchaseOrderListDTO> purchaseOrders;

}
