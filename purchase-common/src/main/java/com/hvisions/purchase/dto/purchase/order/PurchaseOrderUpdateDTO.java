package com.hvisions.purchase.dto.purchase.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.purchase.dto.SysBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description: 采购单修改
 * @author: Jcao
 * @time: 2022/4/7 14:18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "采购单修改")
public class PurchaseOrderUpdateDTO extends SysBaseDTO {

    @ApiModelProperty(value = "采购单id")
    @NotNull(message = "采购单id不能为空！")
    private Integer orderId;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

    @ApiModelProperty(value = "投料类型：1-生产用料、2-试验用料、3-科研用料")
    private String feedType;

    @ApiModelProperty(value = "线下采购时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offlinePurchaseTime;

    @ApiModelProperty(value = "是否审批：true审批，false不审批")
    private Boolean isAudit;

    @ApiModelProperty(value = "是否是app修改：true-是app，false-不是")
    @Builder.Default
    private Boolean isApp = false;

    @ApiModelProperty(value = "发起名称")
    private String launchName;

    @ApiModelProperty(value = "审批人id")
    private Integer auditId;

    @ApiModelProperty(value = "审批人名称")
    private String auditName;

    @ApiModelProperty(value = "采购单集合")
    List<PurchaseOrderListDTO> purchaseOrders;

}
