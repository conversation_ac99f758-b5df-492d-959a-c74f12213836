package com.hvisions.purchase.dto.purchase.order.detail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 采购单详情
 * @author: Jcao
 * @time: 2022/4/7 14:18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "采购单详情")
public class PurchaseOrderDetailDTO {

    @ApiModelProperty(value = "采购单详情id")
    private Integer id;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

    @ApiModelProperty(value = "物料单位")
    private String uom;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "已到货重量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "入库重量")
    private BigDecimal inboundWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "库存地点编码")
    private String locationCode;

    @ApiModelProperty(value = "库存地点")
    private String locationName;

    @ApiModelProperty(value = "工厂编码")
    private String factoryCode;

    @ApiModelProperty(value = "工厂名称")
    private String factoryName;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

    @ApiModelProperty(value = "车辆信息列表")
    private List<PurchaseOrderVehicleDetailDTO> VehicleDetails;

}
