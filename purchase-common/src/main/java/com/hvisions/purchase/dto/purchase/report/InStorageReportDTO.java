package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 原辅料入库报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "原辅料入库报表dto")
public class InStorageReportDTO {

    @ApiModelProperty(value = "送货单id")
    private Integer id;

    @ApiModelProperty(value = "日送货计划号")
    private String planNumber;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "车辆状态： 0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、5-待处理、6-拒收、7-复检、8-特殊退货")
    private String state;

    @ApiModelProperty(value = "质检状态:0- 待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;

    @ApiModelProperty(value = "质检结果")
    private String qualityResult;

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "入场时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date admissionTime;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "入库仓Id")
    private String warehouseId;

    @ApiModelProperty(value = "入库仓号")
    private String warehouseCode;

    @ApiModelProperty(value = "小麦认证批次")
    private String wheatCertificateBatch;

}
