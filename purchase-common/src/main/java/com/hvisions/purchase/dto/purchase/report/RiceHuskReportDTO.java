package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 稻壳报表dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "稻壳报表dto")
public class RiceHuskReportDTO extends DeliveryVehicleInfoDTO {

    @ApiModelProperty(value = "水分")
    private String water;

    @ApiModelProperty(value = "粗细度（%）")
    private String coarseness;

    @ApiModelProperty(value = "夹杂物（%）")
    private String inclusion;

    @ApiModelProperty(value = "异色粒（%）")
    private String particle;

    @ApiModelProperty(value = "感观")
    private String sensory;

}
