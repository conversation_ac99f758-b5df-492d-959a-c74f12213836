package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 高粱报表dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "高粱报表dto")
public class SorghumReportDTO extends DeliveryVehicleInfoDTO {

    @ApiModelProperty(value = "容重（g/L）")
    private String density;

    @ApiModelProperty(value = "水分（%）")
    private String water;

    @ApiModelProperty(value = "杂质（%）")
    private String impurity;

    @ApiModelProperty(value = "不完善粒（%）")
    private String Imperfect;

    @ApiModelProperty(value = "生霉病率（%）")
    private String moldRate;

    @ApiModelProperty(value = "生芽粒（%）")
    private String sproutedKernel;

    @ApiModelProperty(value = "感观")
    private String perception;

}
