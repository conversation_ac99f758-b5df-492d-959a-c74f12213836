package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 送货车报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "送货车报表dto")
public class VehicleReportDTO {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "计划送货数量(t)")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "质检状态")
    private String inspectState;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;
    
    @ApiModelProperty(value = "不合格描述")
    private String rawMaterialsCommit;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "扣重")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "收货净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "车辆状态")
    private String state;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "质检单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "sap采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "不合格原因--质检结论")
    private String defectiveReason;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

}
