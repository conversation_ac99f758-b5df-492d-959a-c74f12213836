package com.hvisions.purchase.dto.purchase.report;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hvisions.common.dto.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 送货车报表查询dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "送货车报表查询dto")
public class VehicleReportQueryDTO extends PageInfo {

    @ApiModelProperty(value = "送货单类型：1-高粱、2-稻壳、3-小麦,逗号隔开")
    private String types;

    @ApiModelProperty(value = "物料编码集合，逗号隔开")
    private String materialCodes;

    @ApiModelProperty(value = "供应商编码集合，逗号隔开")
    private String vendorCodes;

    @ApiModelProperty(value = "送货计划号")
    private String planNumber;

    @ApiModelProperty(value = "送货开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date admissionStartTime;

    @ApiModelProperty(value = "送货结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date admissionEndTime;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "车辆状态： 0- 未入场、1-已入场、2-可卸货、3-卸货完成、4-出门、5-待处理、6-拒收、7-复检、8-特殊退货")
    private String state;

    @ApiModelProperty(value = "质检状态:0- 待检验、1-质检中、2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;

    @ApiModelProperty(value = "检验任务单号")
    private String inspectionOrder;



}
