package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 过磅记录dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "过磅记录dto")
public class WeightRecordReportDTO {

//    @ApiModelProperty(value = "送货单号")
//    private String deliveryNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "计划送货数量")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "实际送货数量")
    private BigDecimal actualWeight;

    @ApiModelProperty(value = "入场时间")
    private Date admissionTime;

    @ApiModelProperty(value = "出场时间")
    private Date appearanceTime;

    @ApiModelProperty(value = "皮重")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "毛重")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "扣重")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "收货净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "车辆状态")
    private String state;

    @ApiModelProperty(value = "质检状态")
    private String inspectState;

    @ApiModelProperty(value = "质检单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "检验单id")
    private Integer inspectionId;

    @ApiModelProperty(value = "质检结论")
    private String qualityResult;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "SAP采购订单号")
    private String sapOrder;

    @ApiModelProperty(value = "送货地点")
    private String locationName;

    @ApiModelProperty(value = "合同号")
    private String contractNumber;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

}
