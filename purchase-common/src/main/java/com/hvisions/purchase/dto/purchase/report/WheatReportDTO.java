package com.hvisions.purchase.dto.purchase.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 高粱报表dto
 * @author: Jcao
 * @time: 2022/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "高粱报表dto")
public class WheatReportDTO extends DeliveryVehicleInfoDTO {

    @ApiModelProperty(value = "容重（g/L）")
    private BigDecimal density;

    @ApiModelProperty(value = "水分（%）")
    private BigDecimal water;

    @ApiModelProperty(value = "杂质（%）")
    private BigDecimal impurity;

    @ApiModelProperty(value = "无机杂质（%）")
    private BigDecimal inorganicImpurity;

    @ApiModelProperty(value = "不完善粒（%）")
    private BigDecimal imperfect;

    @ApiModelProperty(value = "小麦赤霉病粒（%）")
    private BigDecimal moldRate;

    @ApiModelProperty(value = "生芽粒（%）")
    private BigDecimal sproutedKernel;

    @ApiModelProperty(value = "感观")
    private String perception;

}
