package com.hvisions.purchase.dto.purchase.wheat.report;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: yyy
 * @time: 2022/8/31 13:47
 */

@Data
@EqualsAndHashCode
@ApiModel(description = "车辆导入DTO")
public class VehicleImportDTO {

    @ApiModelProperty(value = "车牌号")
    @NotNull(message = "车牌号不能为空")
    private String licensePlateNumber;

    @ApiModelProperty(value = "预计送货数量")
    @NotNull(message = "预计送货数量不能为空")
    private BigDecimal estimatedNumber;

    @ApiModelProperty(value = "物料id")
    @NotNull(message = "物料id不能为空")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    @ApiModelProperty(value = "操作状态：0-新增，1-修改，2-删除")
    private String operateState;

}
