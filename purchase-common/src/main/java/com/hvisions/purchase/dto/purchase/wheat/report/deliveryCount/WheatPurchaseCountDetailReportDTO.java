package com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦送货车次汇总数据详细")
public class WheatPurchaseCountDetailReportDTO {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "合格率（%）")
    private Integer passRate;

}
