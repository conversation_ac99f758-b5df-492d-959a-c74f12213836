package com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @Description: 小麦送货车次统计
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦送货车次统计")
public class WheatPurchaseCountMonthReportDTO {

    @ApiModelProperty(value = "月份")
    private String month;

    @ApiModelProperty(value = "合格率（%）")
    private Integer passRate;

}
