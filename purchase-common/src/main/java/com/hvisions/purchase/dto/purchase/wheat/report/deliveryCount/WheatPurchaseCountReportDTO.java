package com.hvisions.purchase.dto.purchase.wheat.report.deliveryCount;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 小麦送货车次统计
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦送货车次统计")
public class WheatPurchaseCountReportDTO {

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "送货车次")
    private Integer deliveryNum;

    @ApiModelProperty(value = "退货车次")
    private Integer refuseNum;

    @ApiModelProperty(value = "各月合格率")
    private List<WheatPurchaseCountMonthReportDTO> monthPassRate;

}
