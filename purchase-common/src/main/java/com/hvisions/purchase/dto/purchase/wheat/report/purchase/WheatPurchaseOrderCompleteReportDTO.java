package com.hvisions.purchase.dto.purchase.wheat.report.purchase;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 小麦采购订单完成情况报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦采购订单完成情况报表dto")
public class WheatPurchaseOrderCompleteReportDTO {

    @ApiModelProperty(value = "采购单id")
    private Integer id;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "采购订单号")
    private String orderNo;

    @ApiModelProperty(value = "sap订单号")
    private String sapOrder;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "已收货数量")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;
}
