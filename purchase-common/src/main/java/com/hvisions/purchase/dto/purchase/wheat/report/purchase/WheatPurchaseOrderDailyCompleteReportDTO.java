package com.hvisions.purchase.dto.purchase.wheat.report.purchase;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 小麦采购订单完成情况报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦采购订单完成情况报表dto")
public class WheatPurchaseOrderDailyCompleteReportDTO {

    @ApiModelProperty(value = "要货日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "采购总数")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "单价基数")
    private BigDecimal unitPriceBase;

    @ApiModelProperty(value = "已到货重量")
    private BigDecimal arrivalWeight;

    @ApiModelProperty(value = "过账数量")
    private BigDecimal postingQuantity;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

}
