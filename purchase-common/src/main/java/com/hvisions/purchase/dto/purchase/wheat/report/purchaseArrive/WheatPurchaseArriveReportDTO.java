package com.hvisions.purchase.dto.purchase.wheat.report.purchaseArrive;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 小麦采购到货报表dto
 * @author: yyy
 * @time: 2022/7/19 14:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦采购到货报表dto")
public class WheatPurchaseArriveReportDTO {

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal quantitySupplied;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalWeight;

}
