package com.hvisions.purchase.dto.purchase.wheat.report.rate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description: 小麦合格率车辆基础数据dto
 * @author: yyy
 * @time: 2024/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦合格率车辆基础数据dto")
public class WheatRateBaseDataDTO {

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "检验结果：2-合格、3-不合格")
    private String inspectState;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "要货日期")
    private String demandDate;

}
