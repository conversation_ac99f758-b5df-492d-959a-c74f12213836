package com.hvisions.purchase.dto.purchase.wheat.report.rate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 小麦供应合格率dto
 * @author: yyy
 * @time: 2024/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦供应合格率dto")
public class WheatSupplyPassRateDTO {

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "送货车次")
    private Integer vehicleNum;

    @ApiModelProperty(value = "合格车次")
    private Integer passNum;

    @ApiModelProperty(value = "不合格车次")
    private Integer noPassNum;

    @ApiModelProperty(value = "合格率")
    private BigDecimal rate;

    @ApiModelProperty(value = "要货日期")
    private String demandDate;

    @ApiModelProperty(value = "供应商车次详情")
    List<WheatSupplyPassRateVendorDTO> vendorDTOList;

}
