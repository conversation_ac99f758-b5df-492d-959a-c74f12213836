package com.hvisions.purchase.dto.purchase.wheat.report.rate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @Description: 小麦供应合格率供应商详情dto
 * @author: yyy
 * @time: 2024/6/8 11:10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "小麦供应合格率供应商详情dto")
public class WheatSupplyPassRateVendorDTO {

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "要货日期")
    private String demandDate;

    @ApiModelProperty(value = "送货车次")
    private Integer vehicleNum;

    @ApiModelProperty(value = "合格车次")
    private Integer passNum;

    @ApiModelProperty(value = "不合格车次")
    private Integer noPassNum;

    @ApiModelProperty(value = "合格率")
    private BigDecimal rate;

}
