package com.hvisions.purchase.dto.purchase.wheat.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(description = "入仓任务过磅车详情列表")
public class WarehouseDeliveryCarDTO {

    @ApiModelProperty(value = "送货单id")
    private Integer id;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料类型名称")
    private String materialTypeName;

    @ApiModelProperty(value = "物料类型名称")
    private String wheatNumber;

    @ApiModelProperty(value = "修改日期")
    private Date updateTime;

    @ApiModelProperty(value = "卸货日期")
    private Date unloadRecordTime;

    @ApiModelProperty(value = "日送货计划单号")
    private String planNumber;

    @ApiModelProperty(value = "需求日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;

    @ApiModelProperty(value = "供应商编码")
    private String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

    @ApiModelProperty(value = "送货单号")
    private String deliveryNumber;

    @ApiModelProperty(value = "车牌号")
    private String licensePlateNumber;

    @ApiModelProperty(value = "批次")
    private String batch;

    @ApiModelProperty(value = "车辆状态： 0- 待检验（没有取样）、1-质检中（已取样之间）、2-合格（质检合格）、3-不合格（质检结果不合格）")
    private String inspectState;

    @ApiModelProperty(value = "质检单号")
    private String inspectionOrder;

    @ApiModelProperty(value = "样品编码")
    private String samplingCode;

    @ApiModelProperty(value = "检验任务id")
    private Integer inspectionId;

    @ApiModelProperty(value = "毛重（入场重量）")
    private BigDecimal grossWeight;

    @ApiModelProperty(value = "出场重量（皮重）")
    private BigDecimal appearanceWeight;

    @ApiModelProperty(value = "离厂净重")
    private BigDecimal leaveNetWeight;

    @ApiModelProperty(value = "扣重重量")
    private BigDecimal buckleWeight;

    @ApiModelProperty(value = "杂质重量")
    private BigDecimal impurityWeight;

    @ApiModelProperty(value = "最终净重")
    private BigDecimal netWeight;

    @ApiModelProperty(value = "过账状态 0-未过账、1-已过账")
    private String postingState;
}
