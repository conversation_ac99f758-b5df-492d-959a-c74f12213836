package com.hvisions.purchase.dto.purchase.wheat.vehicle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @author: yyy
 * @time: 2022/8/31 13:47
 */

@Data
@EqualsAndHashCode
@ApiModel(description = "小麦过磅详情dto")
public class WheatWeighDetailDTO {

    @ApiModelProperty(value = "过磅重量")
    private BigDecimal overweightWeight;

    @ApiModelProperty(value = "过磅次数")
    private Integer frequency;

    @ApiModelProperty(value = "供应商名称")
    private String vendorName;

}
