package com.hvisions.purchase.dto.purchase.wheat.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: yyy
 * @time: 2022/8/31 13:47
 */

@Data
@EqualsAndHashCode
@ApiModel(description = "小麦过磅查询dto")
public class WheatWeighQueryDTO {

    @ApiModelProperty(value = "入仓任务id")
    private Integer warehouseTaskId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

}
