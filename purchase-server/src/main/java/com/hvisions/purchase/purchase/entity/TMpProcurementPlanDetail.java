package com.hvisions.purchase.purchase.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hvisions.purchase.entity.SysBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 年底需求月需求信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_mp_procurement_plan_detail")
@ApiModel(value="TMpProcurementPlanDetail对象", description="")
public class TMpProcurementPlanDetail extends SysBase {

    @ApiModelProperty(value = "计划id")
    private Integer planId;

    @ApiModelProperty(value = "计划编号")
    private String planCode;

    @ApiModelProperty(value = "要货周期")
    private String yearMonthStr;

    @ApiModelProperty(value = "基地 HY:黄舣;LH:罗汉;CQ:城区;ZQ制曲中心")
    private String baseName;

    @ApiModelProperty(value = "物料代码")
    private String materialCode;

    @ApiModelProperty(value = "物料id")
    private Integer materialId;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQuantity;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
