package com.hvisions.purchase.purchase.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hvisions.auth.dto.user.UserBaseDTO;
import com.hvisions.brewage.feign.plan.MonthPlanClient;
import com.hvisions.brewage.plan.dto.PurchasePlanMaterialDTO;
import com.hvisions.brewage.plan.dto.PurchasePlanReq;
import com.hvisions.common.dto.ExcelExportDto;
import com.hvisions.common.dto.ExtendColumnInfo;
import com.hvisions.common.exception.BaseKnownException;
import com.hvisions.common.utils.DtoMapper;
import com.hvisions.common.utils.HvExportUtil;
import com.hvisions.common.utils.PageHelperUtil;
import com.hvisions.common.vo.ResultVO;
import com.hvisions.materialsmsd.materials.client.MaterialClient;
import com.hvisions.materialsmsd.materials.dto.MaterialDTO;
import com.hvisions.materialsmsd.materials.dto.QueryDTO;
import com.hvisions.powder.dto.productionPlan.plan.year.YearPlanFeedDTO;
import com.hvisions.product.ProductPlanClient;
import com.hvisions.product.WarehouseClient;
import com.hvisions.purchase.advice.UserAuditorAware;
import com.hvisions.purchase.dto.purchase.order.PurchaseOrderListDTO;
import com.hvisions.purchase.dto.purchase.plan.*;
import com.hvisions.purchase.dto.purchase.plan.audit.ProcurementPlanAuditDTO;
import com.hvisions.purchase.dto.purchase.plan.audit.ProcurementPlanAuditRecordDTO;
import com.hvisions.purchase.dto.purchase.plan.audit.ProcurementPlanSubmitAuditDTO;
import com.hvisions.purchase.dto.purchase.safety.stock.MaterialStockInfoDTO;
import com.hvisions.purchase.purchase.consts.MaterialConst;
import com.hvisions.purchase.purchase.consts.ProcurementPlanState;
import com.hvisions.purchase.purchase.dao.*;
import com.hvisions.purchase.purchase.entity.TMpProcurementAudit;
import com.hvisions.purchase.purchase.entity.TMpProcurementPlanDetail;
import com.hvisions.purchase.purchase.entity.TMpProcurementPlanning;
import com.hvisions.purchase.purchase.entity.TMpPurchaseOrder;
import com.hvisions.purchase.purchase.service.ProcurementPlanService;
import com.hvisions.purchase.utils.DateUtil;
import com.hvisions.purchase.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.hvisions.powder.consts.CommonConsts.LOGIN_HINT;

/**
 * @Description: 采购计划
 * @author: Jcao
 * @time: 2022/4/2 17:09
 */
@Slf4j
@Service
public class ProcurementPlanServiceImpl implements ProcurementPlanService {

    @Resource
    private SerialNumberMapper serialNumberMapper;

    @Resource
    private MonthPlanClient monthPlanClient;

    @Resource
    private ProcurementPlanMapper procurementPlanMapper;

    @Resource
    private ProcurementAuditMapper procurementAuditMapper;

    @Resource
    private PurchaseOrderMapper purchaseOrderMapper;

    @Resource
    private SafetyStockMapper safetyStockMapper;

    @Resource
    private ProductPlanClient productPlanClient;

    @Resource
    private WarehouseClient warehouseClient;

    @Resource
    private MaterialClient materialClient;

    @Autowired
    UserAuditorAware userAuditorAware;

    @Resource
    private TMpProcurementPlanDetailMapper procurementPlanDetailMapper;

    /*
     * @Description: 分页查询采购计划
     *
     * <AUTHOR>
     * @param procurementPlanPageQueryDTO:
     * @return org.springframework.data.domain.Page<com.hvisions.purchase.dto.purchase.procurement.plan.ProcurementPlanPageDTO>
     */
    @Override
    public Page<ProcurementPlanPageDTO> getProcurementPlanPageList(ProcurementPlanPageQueryDTO procurementPlanPageQueryDTO) {
        Page<ProcurementPlanPageDTO> page = PageHelperUtil.getPage(procurementPlanMapper::getProcurementPlanPageList, procurementPlanPageQueryDTO, ProcurementPlanPageDTO.class);
        for (ProcurementPlanPageDTO procurementPlanPageDTO : page.getContent()) {
            //获取采购订单信息
            List<TMpPurchaseOrder> tMpPurchaseOrders = purchaseOrderMapper.selectList(new LambdaUpdateWrapper<TMpPurchaseOrder>().eq(TMpPurchaseOrder::getPlanningId, procurementPlanPageDTO.getId()));
            List<PurchaseOrderListDTO> list = DtoMapper.convertList(tMpPurchaseOrders, PurchaseOrderListDTO.class);
            procurementPlanPageDTO.setPurchaseOrderList(list);
        }
        return page;
    }

    @Override
    public ProcurementPlanPageDTO getProcurementPlanDetail(Integer id) {
        return procurementPlanMapper.getProcurementPlanDetail(id);
    }

    /*
     * @Description: 采购计划导出
     *
     * <AUTHOR>
     * @param procurementPlanPageQueryDTO:
     * @return com.hvisions.common.dto.ExcelExportDto
     */
    @Override
    public ExcelExportDto exportData(ProcurementPlanPageQueryDTO procurementPlanPageQueryDTO) {
        List<ProcurementPlanPageDTO> dataList = getProcurementPlanPageList(procurementPlanPageQueryDTO).getContent();
        List<ExtendColumnInfo> extendColumnInfoList = Collections.emptyList();
        ExcelExportDto excelExportDto = HvExportUtil.exportData(dataList, ProcurementPlanPageDTO.class, extendColumnInfoList);
        excelExportDto.setFileName("采购计划列表");
        return excelExportDto;
    }


    /*
     * @Description: 根据物料id获取库存信息
     *
     * <AUTHOR>
     * @param materialId: 物料id
     * @return com.hvisions.purchase.dto.purchase.safety.stock.MaterialStockInfoDTO
     */
    @Override
    public MaterialStockInfoDTO getMaterialStockInfo(Integer materialId) {
        MaterialStockInfoDTO materialStockInfo = safetyStockMapper.getMaterialStockInfo(materialId);
        if (materialStockInfo != null) {
            materialStockInfo.setStockQuantity(new BigDecimal(0));
            materialStockInfo.setTransitQuantity(procurementPlanMapper.getTransitQuantity(materialId) == null ? new BigDecimal(0) : procurementPlanMapper.getTransitQuantity(materialId));
        } else {
            materialStockInfo = new MaterialStockInfoDTO();
            materialStockInfo.setMaterialId(materialId);
            materialStockInfo.setTransitQuantity(new BigDecimal(0));
            materialStockInfo.setStockQuantity(new BigDecimal(0));
            materialStockInfo.setSafetyValue(new BigDecimal(0));
        }
        return materialStockInfo;
    }

    /*
     * @Description: 根据时间获取生产计划需求数量
     *
     * <AUTHOR>
     * @param cycleQueryDTO:
     * @return java.util.List<com.hvisions.purchase.dto.purchase.safety.stock.MaterialStockInfoDTO>
     */
    @Override
    public List<MaterialStockInfoDTO> getMaterialDemandInfo(CycleQueryDTO cycleQueryDTO) {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern("yyyy-MM");
        String yearMonth = cycleQueryDTO.getEndTime().format(formatters);
        // 根据生产计划获取计划需求数量
        List<PurchasePlanMaterialDTO> purchasePlan = null;
        PurchasePlanReq req = new PurchasePlanReq();
        req.setBeginDate(cycleQueryDTO.getBeginTime());
        req.setEndDate(cycleQueryDTO.getEndTime());
        log.info("获取物料需求，请求参数:" + JSONObject.toJSONString(req));
        ResultVO<List<PurchasePlanMaterialDTO>> resultVo = monthPlanClient.purchasePlan(req);
        log.info("获取物料需求，返回结果:" + JSONObject.toJSONString(resultVo));
        if (resultVo.getCode() != 200 || resultVo.getData().size() == 0) {
            throw new BaseKnownException(10000, "获取月计划物料需求数量异常");
        }
        purchasePlan = resultVo.getData();
        log.info("获取月计划物料需求数量<{}>", purchasePlan.toString());

        List<MaterialStockInfoDTO> materialStockInfos = new ArrayList<>();
        log.info("获取计划采购物料：<{}>", purchasePlan.toString());
        if (purchasePlan != null && purchasePlan.size() > 0) {
            List<Integer> materialIds = procurementPlanMapper.getMaterialIds();
            purchasePlan.stream().filter(item ->
                    materialIds.stream().anyMatch(id -> item.getMaterialId().intValue() == id.intValue())
            ).forEach(item -> {
                MaterialStockInfoDTO materialStockInfo = getMaterialStockInfo(item.getMaterialId());
                materialStockInfo.setMaterialCode(item.getMaterialCode());
                materialStockInfo.setMaterialName(item.getMaterialName());
                materialStockInfo.setDemandQuantity(item.getTotalQuality());
                // 计算获取采购数量
                BigDecimal procurementNumber = materialStockInfo.getDemandQuantity()
                        .subtract(materialStockInfo.getTransitQuantity())
                        .subtract(materialStockInfo.getStockQuantity())
                        .add(materialStockInfo.getSafetyValue());
                materialStockInfo.setProcurementNumber(procurementNumber.setScale(2, RoundingMode.HALF_UP));
                // 只有采购数量大于0，才进行插入
                if (procurementNumber.signum() > 0) {
                    materialStockInfos.add(materialStockInfo);
                }
            });
        }
        // 点击新增时要去获取制曲的年度生产计划中的小麦用料需求，并自动添加用户选择周期内的小麦采购需求，其他与现有功能保持一致，
        List<MaterialStockInfoDTO> wheatRequirementList = getWheatRequirementList(yearMonth);
        materialStockInfos.addAll(wheatRequirementList);
        return materialStockInfos;
    }

    /**
     * @Description 根据年月获取小麦物料需求数量
     *
     * <AUTHOR>
     * @Date 2024-6-12 9:41
     * @param yearMonth
     * @return java.util.List<com.hvisions.purchase.dto.purchase.safety.stock.MaterialStockInfoDTO>
     **/
    List<MaterialStockInfoDTO> getWheatRequirementList(String yearMonth) {
        List<MaterialStockInfoDTO> wheatMaterialStockInfo = new ArrayList<>();

        // 获取小麦计划需求数量
        ResultVO<YearPlanFeedDTO> resultVO = productPlanClient.getDemandWheatNew(yearMonth);
        log.info("小麦计划需求{}", JSONObject.toJSONString(resultVO));
        if (resultVO.getCode() == 200 && resultVO.getData() != null) {
            YearPlanFeedDTO yearPlanFeedDTO = resultVO.getData();
            if (yearPlanFeedDTO.getQF1WheatDemand() != null && yearPlanFeedDTO.getQF1WheatDemand().compareTo(new BigDecimal(0)) > 0) {
                //LQM1
                MaterialStockInfoDTO materialStockInfoDTO = new MaterialStockInfoDTO();
                BigDecimal transitQuantity = procurementPlanMapper.getTransitQuantity(materialStockInfoDTO.getMaterialId());
                materialStockInfoDTO.setTransitQuantity(StringUtil.isEmpty(transitQuantity) ? new BigDecimal(0) : transitQuantity);
                materialStockInfoDTO.setMaterialCode(MaterialConst.LQM1);
                materialStockInfoDTO.setDemandQuantity(yearPlanFeedDTO.getQF1WheatDemand().multiply(new BigDecimal(1000)));
                QueryDTO queryDTO = new QueryDTO();
                queryDTO.setMaterialCode(MaterialConst.LQM1);
                MaterialDTO lqm1MaterialDto = materialClient.getMaterialByNameOrCode(queryDTO).getData().getContent().get(0);
                materialStockInfoDTO.setMaterialId(lqm1MaterialDto.getId());
                materialStockInfoDTO.setMaterialName(lqm1MaterialDto.getMaterialName());
                materialStockInfoDTO.setProcurementNumber(materialStockInfoDTO.getDemandQuantity().setScale(2, RoundingMode.HALF_UP));
                wheatMaterialStockInfo.add(materialStockInfoDTO);
            }
            if (yearPlanFeedDTO.getQF2WheatDemand() != null && yearPlanFeedDTO.getQF2WheatDemand().compareTo(new BigDecimal(0)) > 0) {
                //LQM2
                MaterialStockInfoDTO materialStockInfoDTO = new MaterialStockInfoDTO();
                BigDecimal transitQuantity = procurementPlanMapper.getTransitQuantity(materialStockInfoDTO.getMaterialId());
                materialStockInfoDTO.setTransitQuantity(StringUtil.isEmpty(transitQuantity) ? new BigDecimal(0) : transitQuantity);
                materialStockInfoDTO.setMaterialCode(MaterialConst.LQM2);
                //吨转为KG
                materialStockInfoDTO.setDemandQuantity(yearPlanFeedDTO.getQF2WheatDemand().multiply(new BigDecimal(1000)));
                QueryDTO queryDTO = new QueryDTO();
                queryDTO.setMaterialCode(MaterialConst.LQM2);
                MaterialDTO lqm2MaterialDto = materialClient.getMaterialByNameOrCode(queryDTO).getData().getContent().get(0);
                materialStockInfoDTO.setMaterialId(lqm2MaterialDto.getId());
                materialStockInfoDTO.setMaterialName(lqm2MaterialDto.getMaterialName());
                materialStockInfoDTO.setProcurementNumber(materialStockInfoDTO.getDemandQuantity().setScale(2, RoundingMode.HALF_UP));
                wheatMaterialStockInfo.add(materialStockInfoDTO);
            }

        }
        log.info("小麦计划需求量：" + JSONArray.toJSONString(wheatMaterialStockInfo));
        return wheatMaterialStockInfo;
    }

    /*
     * @Description: 新增采购计划
     *
     * <AUTHOR>
     * @param procurementPlanDTO:
     * @return java.lang.Integer
     */
    @Override
    @Transactional
    public Integer addProcurementPlan(ProcurementPlanSaveDTO procurementPlanSaveDTO) {
        int res = 0;
        List<ProcurementPlanDTO> procurementPlans = procurementPlanSaveDTO.getProcurementPlans();
        for (ProcurementPlanDTO procurementPlanDTO : procurementPlans) {
            TMpProcurementPlanning procurementPlan = DtoMapper.convert(procurementPlanDTO, TMpProcurementPlanning.class);
            String serialNumber = serialNumberMapper.getSerialNumber("t_mp_procurement_planning", 3, true);
            procurementPlan.setPlanCode("JH" + DateUtil.dateFormat(new Date(), "yyMMdd") + serialNumber);
            procurementPlan.setUpdateTime(new Date());
            procurementPlan.setCreateTime(new Date());
            res += procurementPlanMapper.insert(procurementPlan);
            //保存详细信息
            List<ProcurementPlanDetailDTO> planDetailList = procurementPlanDTO.getPlanDetailList();
            for (ProcurementPlanDetailDTO procurementPlanDetailDTO : planDetailList) {
                TMpProcurementPlanDetail detail = DtoMapper.convert(procurementPlanDetailDTO, TMpProcurementPlanDetail.class);
                detail.setPlanId(procurementPlan.getId());
                detail.setPlanCode(procurementPlan.getPlanCode());
                res += procurementPlanDetailMapper.insert(detail);
            }
        }
        return res;
    }

    /*
     * @Description: 修改采购计划
     *
     * <AUTHOR>
     * @param procurementPlanDTO:
     * @return java.lang.Integer
     */
    @Override
    public Integer updateProcurementPlan(ProcurementPlanDTO procurementPlanDTO) {
        TMpProcurementPlanning query = procurementPlanMapper.selectById(procurementPlanDTO.getId());
        if (!"0".equals(query.getState())) {
            throw new BaseKnownException(10000, "只有未提交的数据可以修改");
        }
        TMpProcurementPlanning procurementPlanning = DtoMapper.convert(procurementPlanDTO, TMpProcurementPlanning.class);
        procurementPlanning.setUpdateTime(new Date());
        return procurementPlanMapper.updateById(procurementPlanning);
    }

    @Override
    public Integer adjustProcurementPlan(ProcurementPlanAdjustDTO adjustDTO) {
        TMpProcurementPlanning planning = procurementPlanMapper.selectById(adjustDTO.getId());
        planning.setAdjustPeople(adjustDTO.getAdjustPeople());
        planning.setAdjustQuantity(adjustDTO.getProcurementNumber().subtract(planning.getProcurementNumber()));
        planning.setAdjustRemark(adjustDTO.getAdjustRemark());
        planning.setAdjustTime(new Date());
        planning.setProcurementNumber(adjustDTO.getProcurementNumber());
        return procurementPlanMapper.updateById(planning);
    }

    /*
     * @Description: 根据采购计划id获取审批记录
     *
     * <AUTHOR>
     * @param id: 采购计划id
     * @return java.util.List<com.hvisions.purchase.dto.purchase.procurement.plan.audit.ProcurementPlanAuditRecordDTO>
     */
    @Override
    public List<ProcurementPlanAuditRecordDTO> getProcurementPlanAuditRecord(Integer id) {
        List<TMpProcurementAudit> procurementAuditList = procurementAuditMapper.selectList(new QueryWrapper<TMpProcurementAudit>()
                .eq("procurement_id", id));
        return DtoMapper.convertList(procurementAuditList, ProcurementPlanAuditRecordDTO.class);
    }

    /*
     * @Description: 审批提交
     *
     * <AUTHOR>
     * @param procurementPlanAuditDTO:
     * @return java.lang.Integer
     */
    @Override
    public Integer submitAudit(ProcurementPlanSubmitAuditDTO procurementPlanSubmitAuditDTO) {
        TMpProcurementPlanning procurementPlanning = DtoMapper.convert(procurementPlanSubmitAuditDTO, TMpProcurementPlanning.class);
        procurementPlanning.setState(ProcurementPlanState.TO_BE_AUDIT);
        //提交人
        UserBaseDTO userBaseDTO = userAuditorAware.getCurrentUserAudit()
                .orElseThrow(() -> new BaseKnownException(423001, LOGIN_HINT));
        procurementPlanning.setSubmitUser(userBaseDTO.getUserName());
        procurementPlanning.setSubmitTime(new Date());
        int res = procurementPlanMapper.updateById(procurementPlanning);
        if (res > 0) {
            // 插入采购计划审批过程记录
            TMpProcurementAudit procurementAudit = DtoMapper.convert(procurementPlanSubmitAuditDTO, TMpProcurementAudit.class);
            procurementAudit.setProcurementId(procurementPlanSubmitAuditDTO.getId());
            procurementAudit.setOperateId(procurementPlanSubmitAuditDTO.getCreatorId());
            procurementAudit.setOperateStep("提交审批");
            procurementAudit.setCreateTime(new Date());
            procurementAudit.setUpdateTime(new Date());
            procurementAuditMapper.insert(procurementAudit);
        }
        return res;
    }

    /*
     * @Description: 审批
     *
     * <AUTHOR>
     * @param procurementPlanAuditDTO:
     * @return java.lang.Integer
     */
    @Override
    public Integer audit(ProcurementPlanAuditDTO procurementPlanAuditDTO) {
        Integer res = 0;
        for (Integer id : procurementPlanAuditDTO.getIds()) {
            TMpProcurementPlanning procurementPlanning = new TMpProcurementPlanning();
            procurementPlanning.setId(id);
            // 判断是否通过审批
            if (ProcurementPlanState.result.PASS.equals(procurementPlanAuditDTO.getAuditResult())) {
                // 审批通过，判断是否有检验单，存在只状态为执行中，不存在待执行
                Integer count = purchaseOrderMapper.selectCount(new QueryWrapper<TMpPurchaseOrder>().eq("planning_id", id));
                if (count != null && count > 0) {
                    procurementPlanning.setState(ProcurementPlanState.ALREADY_FINISH);
                } else {
                    procurementPlanning.setState(ProcurementPlanState.TO_BE_CARRY_OUT);
                }
            } else {
                procurementPlanning.setState(ProcurementPlanState.TO_BE_SUBMITTED);
            }
            procurementPlanning.setAuditTime(new Date());
            procurementPlanning.setAuditResult(procurementPlanAuditDTO.getAuditResult());
            procurementPlanning.setRemark(procurementPlanAuditDTO.getRemark());
            res += procurementPlanMapper.updateById(procurementPlanning);
            if (res > 0) {
                // 插入采购计划审批过程记录
                TMpProcurementAudit procurementAudit = DtoMapper.convert(procurementPlanAuditDTO, TMpProcurementAudit.class);
                procurementAudit.setProcurementId(id);
                procurementAudit.setOperateId(procurementPlanAuditDTO.getCreatorId());
                procurementAudit.setOperateStep(ProcurementPlanState.result.PASS.equals(procurementPlanAuditDTO.getAuditResult()) ? "通过" : "驳回");
                procurementAudit.setCreateTime(new Date());
                procurementAudit.setUpdateTime(new Date());
                procurementAuditMapper.insert(procurementAudit);
            }
        }
        return res;
    }


    @Override
    public Integer deletePurchasePlan(Integer id) {
        TMpProcurementPlanning procurementPlanning = procurementPlanMapper.selectById(id);
        if (procurementPlanning.getState().equals(ProcurementPlanState.TO_BE_SUBMITTED)) {
            return procurementPlanMapper.deleteById(id);
        }
        return 0;
    }

    @Override
    public Integer completePurchasePlan(List<Integer> ids) {

        int res = 0;
        for (Integer id : ids) {
            TMpProcurementPlanning procurementPlanning = procurementPlanMapper.selectById(id);
            if (procurementPlanning.getState().equals(ProcurementPlanState.ALREADY_FINISH)) {
                procurementPlanning.setState(ProcurementPlanState.FINISH);
                procurementPlanning.setUpdateTime(new Date());
                res += procurementPlanMapper.updateById(procurementPlanning);
            }
        }
        return res;
    }

    /**
     * 收货进度查询
     * 根据年分4个个季度，然后根据各季度分别汇总
     * 如果跨年的情况，哪一年占用日期多则用哪一年
     * @param id
     * @return
     */
    @Override
    public List<ProcurementProgressDTO> getProgressById(Integer id) {
        List<ProcurementProgressDTO> procurementProgressList = new ArrayList<>();
        TMpProcurementPlanning tMpProcurementPlanning = procurementPlanMapper.selectById(id);
        Date yearEndDate = DateUtil.getYearEndDate(tMpProcurementPlanning.getEndTime());
        //计算时间差，确认拆分的年份
        int i1 = DateUtil.subtractTwoDates(tMpProcurementPlanning.getBeginTime(), yearEndDate);
        int i2 = DateUtil.subtractTwoDates(yearEndDate, tMpProcurementPlanning.getEndTime());
        int year;
        if (i2 <= 0 || i1 > i2) {
            year = DateUtil.year(tMpProcurementPlanning.getBeginTime());
        } else {
            year = DateUtil.year(tMpProcurementPlanning.getEndTime());
        }
        //采购数量
        BigDecimal procurementNumber = tMpProcurementPlanning.getProcurementNumber().divide(new BigDecimal(4), 2, RoundingMode.UP);
        //获取年份对应的季度开始和结束日期 每个季度开始和结束2 4个季度共8
        List<Date> quarterDates = DateUtil.getQuarterDates(year);
        for (int quarter = 0; quarter < quarterDates.size(); quarter+=2) {
            ProcurementProgressDTO progressDTO = new ProcurementProgressDTO();
            progressDTO.setStartTime(quarterDates.get(quarter));
            progressDTO.setEndTime(quarterDates.get(quarter+1));
            progressDTO.setProcurementNumber(procurementNumber);
            //查询时间周期内的数据
            ProcurementProgressDTO query = procurementPlanMapper.selectDeliverySummaryByPlan(tMpProcurementPlanning.getId(), progressDTO.getStartTime(), progressDTO.getEndTime());
            if (query != null) {
                progressDTO.setArrivalQty(query.getArrivalQty());
                progressDTO.setNetWeightQty(query.getNetWeightQty());
            } else {
                progressDTO.setArrivalQty(BigDecimal.ZERO);
                progressDTO.setNetWeightQty(BigDecimal.ZERO);
            }
            procurementProgressList.add(progressDTO);
        }
        return procurementProgressList;
    }

    /***
     * @Description 每天23点将结束时间等于当天的采购计划编程完成
     *
     * <AUTHOR>
     * @Date 2023-10-7 17:42
     * @param
     * @return void
     **/
    @Scheduled(cron = "0 0 23 * * ? ")
    @SchedulerLock(name = "scheduledCompletePurchasePlan")
    public void scheduledCompletePurchasePlan() {
        log.info("每天23点将结束时间等于当天的采购计划编程完成,开始执行");
        LambdaQueryWrapper<TMpProcurementPlanning> wrap = new LambdaQueryWrapper<TMpProcurementPlanning>()
                .eq(TMpProcurementPlanning::getDeleted, false)
                .eq(TMpProcurementPlanning::getEndTime, DateUtil.format(new Date(), "yyyy-MM-dd 00:00:00"));
        List<TMpProcurementPlanning> procurementPlanningList = procurementPlanMapper.selectList(wrap);
        for (TMpProcurementPlanning procurementPlanning : procurementPlanningList) {
            procurementPlanning.setState(ProcurementPlanState.FINISH);
            procurementPlanning.setUpdateTime(new Date());
            procurementPlanMapper.updateById(procurementPlanning);
        }
        log.info("每天23点将结束时间等于当天的采购计划编程完成,执行结束");
    }
}
