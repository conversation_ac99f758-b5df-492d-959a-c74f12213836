<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hvisions.purchase.purchase.dao.TMpMonthDemandDetailMapper">


    <select id="selectByMonthDemand"
            resultType="com.hvisions.purchase.dto.purchase.demand.order.MonthDemandDetailDTO">
        SELECT mdd.*, t.arrival_weight
        from t_mp_month_demand_detail mdd
        left join (select dd.month_demand_id monthDemandId, SUM(di.arrival_weight) arrival_weight
            from t_mp_daily_delivery dd
            LEFT JOIN t_mp_demand_order deo ON deo.id = dd.demand_id AND deo.deleted = 0
            LEFT JOIN t_mp_delivery_item di ON di.delivery_id = dd.id and di.deleted = 0
            where dd.deleted = 0
            <if test="startTime != null and endTime != null">
                AND DATE_FORMAT(deo.demand_date, '%Y-%m-%d') BETWEEN
                DATE_FORMAT(#{startTime}, '%Y-%m-%d') AND DATE_FORMAT(#{endTime}, '%Y-%m-%d')
            </if>
            <if test="materialId != null">
                AND dd.`material_id` = #{materialId}
            </if>
            GROUP BY dd.month_demand_id
        ) t on t.monthDemandId = mdd.id
        where mdd.deleted = 0
        AND mdd.`mouth_demand_id` = #{id}
    </select>

</mapper>